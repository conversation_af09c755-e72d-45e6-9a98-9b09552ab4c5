/**
 * Service Integration Manager
 * 
 * Orchestrates the initialization and integration of all enhanced services:
 * - Centralized configuration management
 * - Enhanced error handling
 * - Comprehensive database service
 * - Intelligent caching
 * - Service health monitoring
 * - Graceful shutdown handling
 * 
 * @version 1.0.0
 */

import centralizedConfig from '../config/centralizedConfig.js';
import enhancedErrorHandlingService from './enhancedErrorHandlingService.js';
import comprehensiveDatabaseService from './comprehensiveDatabaseService.js';
import intelligentCacheService from './intelligentCacheService.js';
import logger from '../utils/logger.js';

// ============================================================================
// SERVICE INTEGRATION MANAGER
// ============================================================================

class ServiceIntegrationManager {
  constructor() {
    this.services = new Map();
    this.isInitialized = false;
    this.healthCheckInterval = null;
    this.shutdownHandlers = [];
    this.initializationOrder = [
      'config',
      'errorHandling',
      'database',
      'cache'
    ];
  }

  /**
   * Initialize all services in the correct order
   */
  async initialize() {
    try {
      logger.info('Starting service integration manager...');

      // Register services
      this.registerServices();

      // Initialize services in order
      for (const serviceName of this.initializationOrder) {
        await this.initializeService(serviceName);
      }

      // Set up health monitoring
      this.startHealthMonitoring();

      // Set up graceful shutdown
      this.setupGracefulShutdown();

      this.isInitialized = true;
      logger.info('Service integration manager initialized successfully');

      return this.getServiceStatus();

    } catch (error) {
      logger.error('Failed to initialize service integration manager:', error);
      throw error;
    }
  }

  /**
   * Register all services
   */
  registerServices() {
    this.services.set('config', {
      instance: centralizedConfig,
      name: 'Centralized Configuration',
      initialized: centralizedConfig.isInitialized,
      critical: true,
      healthCheck: () => centralizedConfig.isValid()
    });

    this.services.set('errorHandling', {
      instance: enhancedErrorHandlingService,
      name: 'Enhanced Error Handling',
      initialized: true, // Always available
      critical: true,
      healthCheck: () => true
    });

    this.services.set('database', {
      instance: comprehensiveDatabaseService,
      name: 'Comprehensive Database',
      initialized: false,
      critical: true,
      healthCheck: () => comprehensiveDatabaseService.isConnected,
      initialize: () => comprehensiveDatabaseService.initialize()
    });

    this.services.set('cache', {
      instance: intelligentCacheService,
      name: 'Intelligent Cache',
      initialized: false,
      critical: false, // Non-critical, can fallback
      healthCheck: () => intelligentCacheService.isConnected || true, // Fallback available
      initialize: () => intelligentCacheService.initialize()
    });

    logger.info('Services registered:', Array.from(this.services.keys()));
  }

  /**
   * Initialize a specific service
   */
  async initializeService(serviceName) {
    const service = this.services.get(serviceName);
    
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }

    if (service.initialized) {
      logger.info(`Service already initialized: ${service.name}`);
      return;
    }

    try {
      logger.info(`Initializing service: ${service.name}`);
      
      if (service.initialize) {
        await service.initialize();
      }
      
      service.initialized = true;
      logger.info(`Service initialized successfully: ${service.name}`);

    } catch (error) {
      const errorMessage = `Failed to initialize ${service.name}: ${error.message}`;
      
      if (service.critical) {
        logger.error(errorMessage);
        throw new Error(errorMessage);
      } else {
        logger.warn(`${errorMessage} (non-critical, continuing...)`);
        service.initialized = false;
      }
    }
  }

  /**
   * Get status of all services
   */
  getServiceStatus() {
    const status = {
      integrationManager: {
        initialized: this.isInitialized,
        timestamp: new Date().toISOString()
      },
      services: {}
    };

    for (const [serviceName, service] of this.services) {
      status.services[serviceName] = {
        name: service.name,
        initialized: service.initialized,
        critical: service.critical,
        healthy: service.healthCheck ? service.healthCheck() : true,
        details: this.getServiceDetails(serviceName, service)
      };
    }

    return status;
  }

  /**
   * Get detailed status for a specific service
   */
  getServiceDetails(serviceName, service) {
    try {
      switch (serviceName) {
        case 'config':
          return {
            environment: centralizedConfig.get('app.env'),
            validationErrors: centralizedConfig.validationErrors.length,
            configuredApis: centralizedConfig.getConfiguredApiKeys()
          };

        case 'errorHandling':
          return enhancedErrorHandlingService.getErrorStats();

        case 'database':
          return comprehensiveDatabaseService.getHealthStatus();

        case 'cache':
          return intelligentCacheService.getStatus();

        default:
          return {};
      }
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Start health monitoring for all services
   */
  startHealthMonitoring() {
    const healthCheckInterval = centralizedConfig.get('monitoring.performance.enabled') ? 60000 : 300000;
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, healthCheckInterval);

    logger.info(`Health monitoring started (interval: ${healthCheckInterval}ms)`);
  }

  /**
   * Perform health check on all services
   */
  async performHealthCheck() {
    const healthStatus = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      services: {}
    };

    let hasUnhealthyService = false;
    let hasCriticalIssue = false;

    for (const [serviceName, service] of this.services) {
      try {
        const isHealthy = service.healthCheck ? service.healthCheck() : true;
        
        healthStatus.services[serviceName] = {
          healthy: isHealthy,
          critical: service.critical,
          initialized: service.initialized
        };

        if (!isHealthy) {
          hasUnhealthyService = true;
          if (service.critical) {
            hasCriticalIssue = true;
          }
        }

      } catch (error) {
        healthStatus.services[serviceName] = {
          healthy: false,
          critical: service.critical,
          initialized: service.initialized,
          error: error.message
        };

        hasUnhealthyService = true;
        if (service.critical) {
          hasCriticalIssue = true;
        }
      }
    }

    // Determine overall health
    if (hasCriticalIssue) {
      healthStatus.overall = 'critical';
    } else if (hasUnhealthyService) {
      healthStatus.overall = 'degraded';
    }

    // Log health status
    if (healthStatus.overall === 'healthy') {
      logger.debug('Health check passed:', healthStatus);
    } else {
      logger.warn('Health check detected issues:', healthStatus);
    }

    // Trigger alerts for critical issues
    if (hasCriticalIssue) {
      this.handleCriticalHealthIssue(healthStatus);
    }

    return healthStatus;
  }

  /**
   * Handle critical health issues
   */
  handleCriticalHealthIssue(healthStatus) {
    logger.error('Critical health issue detected:', healthStatus);
    
    // Create critical error for monitoring
    const criticalError = enhancedErrorHandlingService.createAPIError(
      new Error('Critical service health issue detected'),
      {
        service: 'integration-manager',
        operation: 'health-check',
        category: 'system',
        healthStatus
      }
    );

    // Could trigger additional alerting mechanisms here
    // e.g., send notifications, trigger failover, etc.
  }

  /**
   * Set up graceful shutdown handling
   */
  setupGracefulShutdown() {
    const shutdownHandler = async (signal) => {
      logger.info(`Received ${signal}, initiating graceful shutdown...`);
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGTERM', shutdownHandler);
    process.on('SIGINT', shutdownHandler);
    process.on('SIGUSR2', shutdownHandler); // For nodemon

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      enhancedErrorHandlingService.createAPIError(error, {
        service: 'integration-manager',
        operation: 'uncaught-exception',
        category: 'system'
      });
      
      // Give time for logging then exit
      setTimeout(() => process.exit(1), 1000);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection:', { reason, promise });
      enhancedErrorHandlingService.createAPIError(
        new Error(`Unhandled promise rejection: ${reason}`),
        {
          service: 'integration-manager',
          operation: 'unhandled-rejection',
          category: 'system'
        }
      );
    });

    logger.info('Graceful shutdown handlers registered');
  }

  /**
   * Gracefully shutdown all services
   */
  async shutdown() {
    logger.info('Starting graceful shutdown of all services...');

    // Stop health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Shutdown services in reverse order
    const shutdownOrder = [...this.initializationOrder].reverse();
    
    for (const serviceName of shutdownOrder) {
      const service = this.services.get(serviceName);
      
      if (service && service.instance && typeof service.instance.shutdown === 'function') {
        try {
          logger.info(`Shutting down service: ${service.name}`);
          await service.instance.shutdown();
          logger.info(`Service shut down successfully: ${service.name}`);
        } catch (error) {
          logger.error(`Error shutting down ${service.name}:`, error);
        }
      }
    }

    // Execute custom shutdown handlers
    for (const handler of this.shutdownHandlers) {
      try {
        await handler();
      } catch (error) {
        logger.error('Error in custom shutdown handler:', error);
      }
    }

    this.isInitialized = false;
    logger.info('Graceful shutdown completed');
  }

  /**
   * Add custom shutdown handler
   */
  addShutdownHandler(handler) {
    if (typeof handler === 'function') {
      this.shutdownHandlers.push(handler);
    }
  }

  /**
   * Get service instance by name
   */
  getService(serviceName) {
    const service = this.services.get(serviceName);
    return service ? service.instance : null;
  }

  /**
   * Check if all critical services are healthy
   */
  areAllCriticalServicesHealthy() {
    for (const [, service] of this.services) {
      if (service.critical && (!service.initialized || !service.healthCheck())) {
        return false;
      }
    }
    return true;
  }

  /**
   * Restart a specific service
   */
  async restartService(serviceName) {
    const service = this.services.get(serviceName);
    
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }

    logger.info(`Restarting service: ${service.name}`);

    try {
      // Shutdown if possible
      if (service.instance && typeof service.instance.shutdown === 'function') {
        await service.instance.shutdown();
      }

      // Reinitialize
      service.initialized = false;
      await this.initializeService(serviceName);

      logger.info(`Service restarted successfully: ${service.name}`);
      return true;

    } catch (error) {
      logger.error(`Failed to restart service ${service.name}:`, error);
      throw error;
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const serviceIntegrationManager = new ServiceIntegrationManager();

export default serviceIntegrationManager;
export { ServiceIntegrationManager };
