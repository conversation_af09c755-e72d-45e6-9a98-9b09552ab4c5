import 'dotenv/config';
import express from 'express';
import path from 'path';
import cors from 'cors';
import morgan from 'morgan';
import compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import winston from 'winston';
import 'winston-daily-rotate-file';
import mongoose from 'mongoose';
import Redis from 'ioredis';
import { Server } from 'socket.io';
import http from 'http';
import Bull from 'bull';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import enhanced services
import serviceIntegrationManager from './src/services/serviceIntegrationManager.js';
import centralizedConfig from './src/config/centralizedConfig.js';
import enhancedErrorHandlingService from './src/services/enhancedErrorHandlingService.js';
import intelligentCacheService from './src/services/intelligentCacheService.js';
import comprehensiveDatabaseService from './src/services/comprehensiveDatabaseService.js';

// Import legacy services (to be gradually replaced)
import config from './config/index.js';
import { calculateIndicators } from './utils/indicators.js';
import { generateSignals } from './utils/signals.js';
import { validateInputs } from './utils/validation.js';
import cache from './cache/index.js';

// Import response formatter middleware
import { responseFormatter, performanceTrackingMiddleware, requestIdMiddleware } from './src/middleware/responseFormatter.js';

// Initialize logger using centralized config
const loggerConfig = centralizedConfig.get('monitoring.logging');
const logger = winston.createLogger({
  level: loggerConfig.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    loggerConfig.format === 'json' ? winston.format.json() : winston.format.simple()
  ),
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: loggerConfig.fileRotation.datePattern,
      level: 'error',
      maxFiles: loggerConfig.fileRotation.maxFiles,
      maxSize: loggerConfig.fileRotation.maxSize
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: loggerConfig.fileRotation.datePattern,
      maxFiles: loggerConfig.fileRotation.maxFiles,
      maxSize: loggerConfig.fileRotation.maxSize
    })
  ]
});

// Add console transport in development
if (loggerConfig.console.enabled) {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Configure Redis using centralized config
const redisConfig = centralizedConfig.get('cache.redis');
const redis = redisConfig.enabled ? new Redis({
  host: redisConfig.host,
  port: redisConfig.port,
  password: redisConfig.password,
  db: redisConfig.db,
  retryStrategy: redisConfig.retryStrategy,
  lazyConnect: redisConfig.lazyConnect,
  maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
  connectTimeout: redisConfig.connectTimeout,
  commandTimeout: redisConfig.commandTimeout
}) : null;

// Configure Bull job queues
const marketDataQueue = new Bull('market data processing', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    lazyConnect: true,
    maxRetriesPerRequest: 3,
    connectTimeout: 5000
  }
});

const signalQueue = new Bull('signal generation', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    lazyConnect: true,
    maxRetriesPerRequest: 3,
    connectTimeout: 5000
  }
});

// Job processors
marketDataQueue.process('fetch-market-data', async (job) => {
  const { symbol, timeframe } = job.data;
  logger.info(`Processing market data job for ${symbol}:${timeframe}`);

  try {
    // Fetch market data (implement your API calls here)
    const marketData = await fetchMarketDataFromAPI(symbol, timeframe);

    // Store in database
    if (marketData && marketData.length > 0) {
      await mongoose.connection.db.collection('marketData').insertMany(
        marketData.map(item => ({
          ...item,
          symbol: symbol.toUpperCase(),
          timeframe,
          timestamp: new Date(item.timestamp || Date.now()),
          createdAt: new Date()
        }))
      );

      // Broadcast update via Socket.IO
      if (global.broadcastMarketUpdate) {
        global.broadcastMarketUpdate(symbol, marketData);
      }
    }

    return { success: true, count: marketData?.length || 0 };
  } catch (error) {
    logger.error(`Market data job failed for ${symbol}:${timeframe}`, error);
    throw error;
  }
});

signalQueue.process('generate-signals', async (job) => {
  const { symbol, timeframe } = job.data;
  logger.info(`Processing signal generation job for ${symbol}:${timeframe}`);

  try {
    // Fetch recent market data
    const marketData = await mongoose.connection.db.collection('marketData').find({
      symbol: symbol.toUpperCase(),
      timeframe
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .toArray();

    if (marketData && marketData.length > 0) {
      // Generate signals
      const signals = generateSignals(marketData);

      // Store signals
      if (signals && signals.length > 0) {
        await mongoose.connection.db.collection('signals').insertMany(
          signals.map(signal => ({
            ...signal,
            symbol: symbol.toUpperCase(),
            timeframe,
            createdAt: new Date()
          }))
        );

        // Broadcast signals via Socket.IO
        if (global.broadcastSignal) {
          signals.forEach(signal => {
            global.broadcastSignal(symbol, signal);
          });
        }
      }
    }

    return { success: true, signalCount: signals?.length || 0 };
  } catch (error) {
    logger.error(`Signal generation job failed for ${symbol}:${timeframe}`, error);
    throw error;
  }
});

// Placeholder function for API calls (implement with your actual API)
async function fetchMarketDataFromAPI(symbol, timeframe) {
  // This would integrate with your actual market data APIs
  // For now, return empty array
  logger.warn(`fetchMarketDataFromAPI not implemented for ${symbol}:${timeframe}`);
  return [];
}

const app = express();
const PORT = centralizedConfig.get('app.port');

// Create HTTP server for Socket.IO
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ["http://localhost:3000", "http://127.0.0.1:3000"],
    methods: ["GET", "POST"]
  }
});

// Add request tracking middleware first
app.use(requestIdMiddleware());
app.use(performanceTrackingMiddleware());

// Setup security and performance middleware using centralized config
const securityConfig = centralizedConfig.get('security');
app.use(helmet());
app.use(compression()); // Compress responses
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies with size limit
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Parse URL-encoded bodies

// Configure CORS using centralized settings
app.use(cors({
  origin: securityConfig.cors.origin,
  credentials: securityConfig.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID', 'X-API-Key']
}));

// Add response formatter middleware
app.use(responseFormatter());

// Set up request logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.http(message.trim())
  }
}));

// Serve static files
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// Add rate limiting using centralized config
const rateLimitConfig = centralizedConfig.get('security.rateLimiting');
const apiLimiter = rateLimit({
  windowMs: rateLimitConfig.windowMs,
  max: rateLimitConfig.maxRequestsPerWindow,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'error',
    message: 'Too many requests, please try again later.',
    retryAfter: Math.ceil(rateLimitConfig.windowMs / 1000)
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/api/health';
  }
});
app.use('/api/', apiLimiter);

// Initialize all enhanced services using the service integration manager
const initializeServices = async () => {
  try {
    logger.info('Starting enhanced service initialization...');

    // Initialize all services through the integration manager
    const serviceStatus = await serviceIntegrationManager.initialize();

    logger.info('Enhanced services initialized successfully:', {
      services: Object.keys(serviceStatus.services),
      allHealthy: serviceIntegrationManager.areAllCriticalServicesHealthy()
    });

    return serviceStatus;
  } catch (error) {
    logger.error('Enhanced service initialization failed:', error);

    // Create error using enhanced error handling
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'server',
      operation: 'initialization',
      category: 'system'
    });

    logger.error('Service initialization error details:', apiError);
    process.exit(1);
  }
};

// Initialize services
initializeServices();

// Enhanced health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Get comprehensive service status
    const serviceStatus = serviceIntegrationManager.getServiceStatus();
    const healthCheck = await serviceIntegrationManager.performHealthCheck();

    // Determine overall status
    const isHealthy = serviceIntegrationManager.areAllCriticalServicesHealthy();
    const statusCode = isHealthy ? 200 : 503;

    const response = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: centralizedConfig.get('app.env'),
      version: process.env.npm_package_version || '1.0.0',
      services: serviceStatus.services,
      healthCheck: healthCheck,
      performance: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    };

    res.status(statusCode).json(response);
  } catch (error) {
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'server',
      operation: 'health-check',
      requestId: req.requestId
    });

    logger.error('Health check failed:', apiError);
    res.error('Health check failed', 500, 'HEALTH_CHECK_ERROR');
  }
});

// Detailed service status endpoint
app.get('/api/health/detailed', async (req, res) => {
  try {
    const serviceStatus = serviceIntegrationManager.getServiceStatus();
    const errorStats = enhancedErrorHandlingService.getErrorStats();
    const cacheStatus = intelligentCacheService.getStatus();
    const dbStatus = comprehensiveDatabaseService.getHealthStatus();

    res.success({
      services: serviceStatus,
      errorStatistics: errorStats,
      cachePerformance: cacheStatus,
      databaseHealth: dbStatus,
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      }
    }, 'Detailed health status retrieved successfully');
  } catch (error) {
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'server',
      operation: 'detailed-health-check',
      requestId: req.requestId
    });

    logger.error('Detailed health check failed:', apiError);
    res.error('Detailed health check failed', 500, 'DETAILED_HEALTH_CHECK_ERROR');
  }
});

// Enhanced authentication middleware
const authenticateToken = (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      const apiError = enhancedErrorHandlingService.createAPIError(
        new Error('Access token required'),
        {
          service: 'auth',
          operation: 'authenticate',
          category: 'authentication',
          requestId: req.requestId
        }
      );
      return res.error(apiError.userMessage, 401, 'MISSING_TOKEN');
    }

    const jwtSecret = centralizedConfig.get('security.jwt.secret');
    jwt.verify(token, jwtSecret, (err, user) => {
      if (err) {
        const apiError = enhancedErrorHandlingService.createAPIError(err, {
          service: 'auth',
          operation: 'verify-token',
          category: 'authentication',
          requestId: req.requestId
        });
        return res.error(apiError.userMessage, 403, 'INVALID_TOKEN');
      }

      req.user = user;
      next();
    });
  } catch (error) {
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'auth',
      operation: 'authenticate',
      category: 'authentication',
      requestId: req.requestId
    });
    res.error(apiError.userMessage, 500, 'AUTH_ERROR');
  }
};

// API Routes
const apiRouter = express.Router();

// Authentication Routes
apiRouter.post('/auth/register', async (req, res) => {
  try {
    const { email, password, username, fullName } = req.body;

    // Validate input
    if (!email || !password || !username) {
      const validationError = new Error('Email, password, and username are required');
      const apiError = enhancedErrorHandlingService.createAPIError(validationError, {
        service: 'auth',
        operation: 'register',
        category: 'validation',
        requestId: req.requestId
      });
      return res.error(apiError.userMessage, 400, 'VALIDATION_ERROR');
    }

    // Check if user already exists
    const existingUser = await mongoose.connection.db.collection('users').findOne({
      $or: [{ email: email.toLowerCase() }, { username }]
    });

    if (existingUser) {
      const conflictError = new Error('User already exists with this email or username');
      const apiError = enhancedErrorHandlingService.createAPIError(conflictError, {
        service: 'auth',
        operation: 'register',
        category: 'business_logic',
        requestId: req.requestId
      });
      return res.error(apiError.userMessage, 409, 'USER_EXISTS');
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const newUser = {
      email: email.toLowerCase(),
      username,
      password: hashedPassword,
      fullName: fullName || username,
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
      preferences: {
        symbols: [],
        notifications: true,
        theme: 'light'
      }
    };

    const result = await mongoose.connection.db.collection('users').insertOne(newUser);

    // Generate JWT token using centralized config
    const jwtConfig = centralizedConfig.get('security.jwt');
    const token = jwt.sign(
      {
        userId: result.insertedId,
        email: newUser.email,
        username: newUser.username,
        role: newUser.role
      },
      jwtConfig.secret,
      { expiresIn: jwtConfig.expiresIn }
    );

    // Return user data without password
    const { password: _, ...userWithoutPassword } = newUser;

    res.success({
      user: { ...userWithoutPassword, _id: result.insertedId },
      token
    }, 'User registered successfully', {
      userId: result.insertedId
    });

  } catch (error) {
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'auth',
      operation: 'register',
      requestId: req.requestId,
      endpoint: '/auth/register',
      method: 'POST'
    });

    logger.error('Registration error:', apiError);
    res.error(apiError.userMessage, apiError.statusCode, apiError.errorCode);
  }
});

apiRouter.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Email and password are required'
      });
    }

    // Find user
    const user = await mongoose.connection.db.collection('users').findOne({
      email: email.toLowerCase()
    });

    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await mongoose.connection.db.collection('users').updateOne(
      { _id: user._id },
      { $set: { lastLogin: new Date() } }
    );

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        username: user.username,
        role: user.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      status: 'success',
      data: {
        user: userWithoutPassword,
        token
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed'
    });
  }
});

apiRouter.get('/auth/me', authenticateToken, async (req, res) => {
  try {
    const user = await mongoose.connection.db.collection('users').findOne(
      { _id: req.user.userId },
      { projection: { password: 0 } }
    );

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    res.json({
      status: 'success',
      data: { user }
    });

  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user data'
    });
  }
});

// Market Data endpoint - with enhanced caching and fallback
apiRouter.get('/market/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const limit = parseInt(req.query.limit || '100', 10);

  try {
    // Use cache wrapper to automatically handle cache read/write
    const cacheKey = `market_${symbol}_${timeframe}_${limit}`;
    const cacheTTL = config.CACHE_TTL_BY_TIMEFRAME[timeframe] || config.CACHE_SETTINGS.redis.defaultTTL;

    const data = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // First try to fetch from database
        let marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();

        // If no data in database, try to fetch from external APIs using enhanced fallback
        if (!marketData || marketData.length === 0) {
          try {
            const apiOrder = ['alphaVantage', 'twelveData', 'finnhub'];
            const apiParams = { symbol: symbol.toUpperCase(), timeframe };

            const apiResult = await enhancedFallbackAPIs(apiOrder, apiParams, {
              useIntelligentRouting: true,
              enableCircuitBreaker: true,
              maxRetries: 2
            });

            if (apiResult && apiResult.data) {
              // Store fetched data in database for future use
              const dataToStore = apiResult.data.map(item => ({
                ...item,
                symbol: symbol.toUpperCase(),
                timeframe,
                timestamp: new Date(item.timestamp || Date.now()),
                createdAt: new Date(),
                source: apiResult.source
              }));

              await mongoose.connection.db.collection('marketData').insertMany(dataToStore);
              marketData = dataToStore.slice(0, limit);
            }
          } catch (apiError) {
            const standardError = await standardizedErrorHandler.handleError(apiError, {
              endpoint: `/market/${symbol}/${timeframe}`,
              method: 'GET',
              symbol,
              timeframe
            });

            logger.warn(`Failed to fetch from external APIs: ${standardError.message}`);
            // Continue with empty data rather than failing
          }
        }

        return marketData || [];
      },
      cacheTTL
    );

    res.json({
      status: 'success',
      data,
      meta: {
        symbol: symbol.toUpperCase(),
        timeframe,
        count: data.length,
        cached: true
      }
    });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: `/market/${symbol}/${timeframe}`,
      method: 'GET'
    });

    logger.error(`Error fetching market data: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// Generate indicators endpoint
apiRouter.get('/indicators/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const { indicators: requestedIndicators } = req.query;

  try {
    // Fetch market data
    const marketData = await mongoose.connection.db.collection('marketData').find({
      symbol: symbol.toUpperCase(),
      timeframe
    })
    .sort({ timestamp: -1 })
    .limit(config.APP_SETTINGS.maxDataPoints)
    .toArray();

    if (!marketData || marketData.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }

    // Process indicators
    const result = calculateIndicators(marketData, requestedIndicators);

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error calculating indicators: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to calculate indicators'
    });
  }
});

// Generate trading signals endpoint
apiRouter.get('/signals/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;

  try {
    // Use cache for signals with a shorter TTL than market data
    const cacheKey = `signals_${symbol}_${timeframe}`;
    const cacheTTL = Math.min(config.CACHE_TTL_BY_TIMEFRAME[timeframe] / 2 || 1800, 1800); // Half of market data TTL or 30 min

    const result = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // Fetch market data
        const marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(config.APP_SETTINGS.maxDataPoints)
        .toArray();

        if (!marketData || marketData.length === 0) {
          return null; // Will be handled below
        }

        // Generate signals
        return generateSignals(marketData);
      },
      cacheTTL
    );

    if (!result) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error generating signals: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate trading signals'
    });
  }
});

// Job queue management endpoints
apiRouter.post('/jobs/market-data', authenticateToken, async (req, res) => {
  try {
    const { symbol, timeframe } = req.body;

    if (!symbol || !timeframe) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and timeframe are required'
      });
    }

    // Add job to queue
    const job = await marketDataQueue.add('fetch-market-data', {
      symbol: symbol.toUpperCase(),
      timeframe
    }, {
      delay: 0,
      attempts: 3,
      backoff: 'exponential'
    });

    res.json({
      status: 'success',
      data: {
        jobId: job.id,
        symbol,
        timeframe,
        message: 'Market data fetch job queued'
      }
    });

  } catch (error) {
    logger.error('Error queuing market data job:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to queue market data job'
    });
  }
});

apiRouter.post('/jobs/signals', authenticateToken, async (req, res) => {
  try {
    const { symbol, timeframe } = req.body;

    if (!symbol || !timeframe) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and timeframe are required'
      });
    }

    // Add job to queue
    const job = await signalQueue.add('generate-signals', {
      symbol: symbol.toUpperCase(),
      timeframe
    }, {
      delay: 0,
      attempts: 3,
      backoff: 'exponential'
    });

    res.json({
      status: 'success',
      data: {
        jobId: job.id,
        symbol,
        timeframe,
        message: 'Signal generation job queued'
      }
    });

  } catch (error) {
    logger.error('Error queuing signal generation job:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to queue signal generation job'
    });
  }
});

apiRouter.get('/jobs/status/:jobId', authenticateToken, async (req, res) => {
  try {
    const { jobId } = req.params;

    // Check both queues for the job
    let job = await marketDataQueue.getJob(jobId);
    if (!job) {
      job = await signalQueue.getJob(jobId);
    }

    if (!job) {
      return res.status(404).json({
        status: 'error',
        message: 'Job not found'
      });
    }

    res.json({
      status: 'success',
      data: {
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress(),
        state: await job.getState(),
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : null,
        failedReason: job.failedReason
      }
    });

  } catch (error) {
    logger.error('Error getting job status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get job status'
    });
  }
});

// Enhanced API endpoints using new services

// AI-powered market analysis endpoint
apiRouter.post('/ai/market-analysis', authenticateToken, async (req, res) => {
  try {
    const { symbol, timeframe, marketData, indicators } = req.body;

    if (!symbol || !marketData) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and market data are required'
      });
    }

    const analysis = await consolidatedOpenAIService.generateMarketAnalysis({
      symbol,
      timeframe: timeframe || '1D',
      prices: marketData,
      indicators: indicators || {}
    });

    res.json({
      status: 'success',
      data: analysis,
      meta: {
        symbol,
        timeframe,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/ai/market-analysis',
      method: 'POST'
    });

    logger.error(`AI market analysis error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// AI sentiment analysis endpoint
apiRouter.post('/ai/sentiment-analysis', authenticateToken, async (req, res) => {
  try {
    const { headlines } = req.body;

    if (!headlines || !Array.isArray(headlines)) {
      return res.status(400).json({
        status: 'error',
        message: 'Headlines array is required'
      });
    }

    const sentiment = await consolidatedOpenAIService.analyzeSentiment(headlines);

    res.json({
      status: 'success',
      data: sentiment,
      meta: {
        headlineCount: headlines.length,
        analyzedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/ai/sentiment-analysis',
      method: 'POST'
    });

    logger.error(`AI sentiment analysis error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// AI trading signal generation endpoint
apiRouter.post('/ai/trading-signals', authenticateToken, async (req, res) => {
  try {
    const { symbol, marketData, indicators } = req.body;

    if (!symbol || !marketData || !indicators) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol, market data, and indicators are required'
      });
    }

    const signal = await consolidatedOpenAIService.generateTradingSignal(
      { symbol, currentPrice: marketData.currentPrice, volume: marketData.volume },
      indicators
    );

    res.json({
      status: 'success',
      data: signal,
      meta: {
        symbol,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/ai/trading-signals',
      method: 'POST'
    });

    logger.error(`AI trading signal error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// Unified signal generation endpoint (combines AI + traditional)
apiRouter.post('/signals/unified', authenticateToken, async (req, res) => {
  try {
    const { symbol, marketData, indicators, options = {} } = req.body;

    if (!symbol || !marketData) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and market data are required'
      });
    }

    if (!unifiedSignalService) {
      return res.status(503).json({
        status: 'error',
        message: 'Unified signal service not available'
      });
    }

    const result = await unifiedSignalService.generateUnifiedSignals({
      symbol,
      marketData,
      indicators: indicators || {},
      options
    });

    if (result.success) {
      // Store signals in database
      if (result.signals && result.signals.length > 0) {
        try {
          const signalsToStore = result.signals.map(signal => ({
            ...signal,
            userId: req.user.id,
            createdAt: new Date(),
            updatedAt: new Date()
          }));

          await mongoose.connection.db.collection('signals').insertMany(signalsToStore);

          // Broadcast signals via WebSocket
          result.signals.forEach(signal => {
            if (signal.confidence >= 60) { // Only broadcast high-confidence signals
              broadcastSignal(symbol, signal);

              // Also broadcast to user-specific room
              io.to(`user:${req.user.id}`).emit('signal_update', signal);
            }
          });

        } catch (dbError) {
          logger.error('Error storing signals:', dbError);
          // Continue without storing
        }
      }

      res.json({
        status: 'success',
        data: result.signals,
        metadata: result.metadata
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: result.error || 'Failed to generate unified signals',
        metadata: result.metadata
      });
    }

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/signals/unified',
      method: 'POST'
    });

    logger.error(`Unified signal generation error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// API health status endpoint
apiRouter.get('/health/apis', authenticateToken, async (req, res) => {
  try {
    const healthStatus = enhancedFallbackAPIs.getAPIHealthStatus();
    const keyManagerStats = {};

    // Get stats from all API key managers
    Object.entries(apiKeyManagers).forEach(([provider, manager]) => {
      keyManagerStats[provider] = manager.getStats();
    });

    res.json({
      status: 'success',
      data: {
        apiHealth: healthStatus,
        keyManagers: keyManagerStats,
        errorMetrics: standardizedErrorHandler.getErrorMetrics(),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/health/apis',
      method: 'GET'
    });

    logger.error(`API health check error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// Reset circuit breakers endpoint (admin only)
apiRouter.post('/admin/reset-circuit-breakers', authenticateToken, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Admin access required'
      });
    }

    enhancedFallbackAPIs.resetCircuitBreakers();
    standardizedErrorHandler.clearMetrics();

    res.json({
      status: 'success',
      message: 'Circuit breakers and error metrics reset successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/admin/reset-circuit-breakers',
      method: 'POST'
    });

    logger.error(`Circuit breaker reset error: ${standardError.message}`);
    res.status(500).json({
      status: 'error',
      message: standardizedErrorHandler.getUserFriendlyMessage(standardError),
      errorId: standardError.id
    });
  }
});

// Mount performance monitoring routes
app.use('/api/performance', performanceRoutes);

// Mount enhanced security routes
app.use('/api/auth', enhancedSecurityRoutes);

// Apply authentication middleware to protected API routes
app.use('/api', enhancedSecurityService.authenticateToken.bind(enhancedSecurityService));

// Apply rate limiting to authenticated routes
app.use('/api', enhancedSecurityService.rateLimiters.authenticated);

// Mount API router
app.use('/api', apiRouter);

// Serve static files for the frontend
app.use(express.static(path.join(__dirname, 'public')));

// Handle SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // Handle user authentication and room joining
  socket.on('authenticate', (data) => {
    if (data.userId) {
      socket.userId = data.userId;
      socket.join(`user:${data.userId}`);
      logger.info(`Client ${socket.id} authenticated as user: ${data.userId}`);

      // Send connection confirmation
      socket.emit('authenticated', {
        success: true,
        userId: data.userId,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Join room for real-time market data
  socket.on('join-market', (symbol) => {
    socket.join(`market-${symbol}`);
    logger.info(`Client ${socket.id} joined market room: ${symbol}`);

    // Send recent signals for this symbol
    if (unifiedSignalService) {
      const recentSignals = unifiedSignalService.getRecentSignals(symbol, 3600000); // 1 hour
      if (recentSignals.length > 0) {
        socket.emit('recent-signals', {
          symbol,
          signals: recentSignals.slice(0, 5), // Send last 5 signals
          timestamp: new Date().toISOString()
        });
      }
    }
  });

  // Leave market room
  socket.on('leave-market', (symbol) => {
    socket.leave(`market-${symbol}`);
    logger.info(`Client ${socket.id} left market room: ${symbol}`);
  });

  // Handle signal subscription preferences
  socket.on('update-signal-preferences', (preferences) => {
    socket.signalPreferences = preferences;
    logger.info(`Client ${socket.id} updated signal preferences:`, preferences);
  });

  // Handle manual signal generation request
  socket.on('generate-signals', async (data) => {
    try {
      if (!data.symbol || !data.marketData) {
        socket.emit('signal-error', {
          error: 'Symbol and market data are required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      if (unifiedSignalService) {
        const result = await unifiedSignalService.generateUnifiedSignals({
          symbol: data.symbol,
          marketData: data.marketData,
          indicators: data.indicators || {},
          options: data.options || {}
        });

        socket.emit('signals-generated', {
          symbol: data.symbol,
          signals: result.signals,
          metadata: result.metadata,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('Error generating signals via WebSocket:', error);
      socket.emit('signal-error', {
        error: 'Failed to generate signals',
        timestamp: new Date().toISOString()
      });
    }
  });

  // Handle connection quality check
  socket.on('ping', () => {
    socket.emit('pong', {
      timestamp: new Date().toISOString()
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Function to broadcast market data updates
const broadcastMarketUpdate = (symbol, data) => {
  io.to(`market-${symbol}`).emit('market-update', {
    symbol,
    data,
    timestamp: new Date().toISOString()
  });
};

// Function to broadcast trading signals
const broadcastSignal = (symbol, signal) => {
  io.to(`market-${symbol}`).emit('trading-signal', {
    symbol,
    signal,
    timestamp: new Date().toISOString()
  });
};

// Make broadcast functions available globally
global.broadcastMarketUpdate = broadcastMarketUpdate;
global.broadcastSignal = broadcastSignal;

// Start the server
server.listen(PORT, () => {
  logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  logger.info(`Health check available at http://localhost:${PORT}/health`);
  logger.info(`Socket.IO enabled for real-time updates`);
});

// Enhanced graceful shutdown using service integration manager
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, initiating enhanced graceful shutdown...`);

  // Close HTTP server first
  server.close(() => {
    logger.info('HTTP server closed');
  });

  try {
    // Use service integration manager for coordinated shutdown
    await serviceIntegrationManager.shutdown();
    logger.info('All services shut down successfully');
    process.exit(0);
  } catch (err) {
    logger.error('Error during enhanced shutdown:', err);
    process.exit(1);
  }
};

// Handle termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // For nodemon

// Enhanced error handling for uncaught exceptions
process.on('uncaughtException', (err) => {
  const apiError = enhancedErrorHandlingService.createAPIError(err, {
    service: 'server',
    operation: 'uncaught-exception',
    category: 'system'
  });

  logger.error('Uncaught Exception:', apiError);

  // Give time for logging then exit
  setTimeout(() => process.exit(1), 1000);
});

// Enhanced error handling for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  const apiError = enhancedErrorHandlingService.createAPIError(
    new Error(`Unhandled promise rejection: ${reason}`),
    {
      service: 'server',
      operation: 'unhandled-rejection',
      category: 'system'
    }
  );

  logger.error('Unhandled Promise Rejection:', apiError);
});

export default app; // Export for testing