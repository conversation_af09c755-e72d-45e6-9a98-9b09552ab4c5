/**
 * Error Recovery Service
 * 
 * Advanced error recovery system that provides multiple recovery
 * strategies and fallback options to minimize user disruption.
 * 
 * Features:
 * - Multiple recovery strategies
 * - Automatic fallback mechanisms
 * - User-guided recovery flows
 * - Data preservation during errors
 * - Progressive degradation
 * - Recovery success tracking
 * 
 * @version 1.0.0
 */

import { ErrorType, ErrorCategory, AppError } from '../context/ErrorContext';
import { api } from './enhancedApiService';
import { logRecovery, logUserAction } from '../utils/errorLogger';

// ============================================================================
// INTERFACES
// ============================================================================

export interface RecoveryStrategy {
  id: string;
  name: string;
  description: string;
  priority: number;
  applicableErrors: ErrorType[];
  autoExecute: boolean;
  userConfirmation: boolean;
  estimatedTime: number; // seconds
  successRate: number; // percentage
  execute: (error: AppError, context?: RecoveryContext) => Promise<RecoveryResult>;
}

export interface RecoveryContext {
  feature?: string;
  component?: string;
  userData?: any;
  previousAttempts?: string[];
  userPreferences?: {
    autoRetry: boolean;
    fallbackMode: boolean;
    dataPreservation: boolean;
  };
}

export interface RecoveryResult {
  success: boolean;
  strategy: string;
  message: string;
  data?: any;
  nextSteps?: string[];
  fallbackAvailable?: boolean;
}

export interface FallbackOption {
  id: string;
  name: string;
  description: string;
  limitations: string[];
  execute: (context?: RecoveryContext) => Promise<any>;
}

// ============================================================================
// RECOVERY STRATEGIES
// ============================================================================

const RECOVERY_STRATEGIES: RecoveryStrategy[] = [
  // Network Recovery Strategies
  {
    id: 'network_retry_exponential',
    name: 'Smart Retry',
    description: 'Automatically retry with increasing delays',
    priority: 1,
    applicableErrors: ['network', 'api'],
    autoExecute: true,
    userConfirmation: false,
    estimatedTime: 10,
    successRate: 85,
    execute: async (error: AppError, context?: RecoveryContext): Promise<RecoveryResult> => {
      const maxAttempts = 3;
      const baseDelay = 1000;

      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));

          // Attempt to recreate the original request
          const result = await retryOriginalRequest(error, context);
          
          logRecovery(error.id, 'network_retry_exponential', true);
          
          return {
            success: true,
            strategy: 'network_retry_exponential',
            message: `Successfully recovered after ${attempt} attempt${attempt > 1 ? 's' : ''}`,
            data: result
          };
        } catch (retryError) {
          if (attempt === maxAttempts) {
            logRecovery(error.id, 'network_retry_exponential', false);
            return {
              success: false,
              strategy: 'network_retry_exponential',
              message: 'All retry attempts failed',
              fallbackAvailable: true
            };
          }
        }
      }

      return {
        success: false,
        strategy: 'network_retry_exponential',
        message: 'Retry strategy failed',
        fallbackAvailable: true
      };
    }
  },

  {
    id: 'network_alternative_endpoint',
    name: 'Alternative Data Source',
    description: 'Try getting data from a backup source',
    priority: 2,
    applicableErrors: ['network', 'api'],
    autoExecute: true,
    userConfirmation: false,
    estimatedTime: 5,
    successRate: 70,
    execute: async (error: AppError, context?: RecoveryContext): Promise<RecoveryResult> => {
      try {
        // Try alternative endpoints based on the original request
        const alternativeData = await tryAlternativeEndpoints(error, context);
        
        logRecovery(error.id, 'network_alternative_endpoint', true);
        
        return {
          success: true,
          strategy: 'network_alternative_endpoint',
          message: 'Retrieved data from alternative source',
          data: alternativeData,
          nextSteps: ['Data may be slightly different from the primary source']
        };
      } catch (alternativeError) {
        logRecovery(error.id, 'network_alternative_endpoint', false);
        
        return {
          success: false,
          strategy: 'network_alternative_endpoint',
          message: 'Alternative sources also unavailable',
          fallbackAvailable: true
        };
      }
    }
  },

  // Authentication Recovery Strategies
  {
    id: 'auth_token_refresh',
    name: 'Refresh Authentication',
    description: 'Automatically refresh your login session',
    priority: 1,
    applicableErrors: ['authentication'],
    autoExecute: true,
    userConfirmation: false,
    estimatedTime: 3,
    successRate: 90,
    execute: async (error: AppError, context?: RecoveryContext): Promise<RecoveryResult> => {
      try {
        const response = await api.post('/auth/refresh');
        
        // Update the auth token in the API service
        if (response.token) {
          // Store new token
          localStorage.setItem('authToken', response.token);
          
          // Retry the original request
          const originalResult = await retryOriginalRequest(error, context);
          
          logRecovery(error.id, 'auth_token_refresh', true);
          
          return {
            success: true,
            strategy: 'auth_token_refresh',
            message: 'Session refreshed successfully',
            data: originalResult
          };
        }
        
        throw new Error('No token received');
      } catch (refreshError) {
        logRecovery(error.id, 'auth_token_refresh', false);
        
        return {
          success: false,
          strategy: 'auth_token_refresh',
          message: 'Session refresh failed - please log in again',
          nextSteps: ['Click "Log In" to continue', 'Your work will be saved automatically']
        };
      }
    }
  },

  // Data Recovery Strategies
  {
    id: 'data_cache_fallback',
    name: 'Use Cached Data',
    description: 'Show previously loaded data while we fix the issue',
    priority: 3,
    applicableErrors: ['network', 'api', 'data'],
    autoExecute: true,
    userConfirmation: false,
    estimatedTime: 1,
    successRate: 60,
    execute: async (error: AppError, context?: RecoveryContext): Promise<RecoveryResult> => {
      try {
        const cachedData = getCachedData(error, context);
        
        if (cachedData) {
          logRecovery(error.id, 'data_cache_fallback', true);
          
          return {
            success: true,
            strategy: 'data_cache_fallback',
            message: 'Showing cached data from your last session',
            data: cachedData,
            nextSteps: [
              'Data may be slightly outdated',
              'We\'ll update it automatically when the connection is restored'
            ]
          };
        }
        
        throw new Error('No cached data available');
      } catch (cacheError) {
        logRecovery(error.id, 'data_cache_fallback', false);
        
        return {
          success: false,
          strategy: 'data_cache_fallback',
          message: 'No cached data available',
          fallbackAvailable: true
        };
      }
    }
  },

  // User-Guided Recovery Strategies
  {
    id: 'user_guided_recovery',
    name: 'Guided Recovery',
    description: 'Step-by-step recovery assistance',
    priority: 4,
    applicableErrors: ['network', 'api', 'data', 'validation'],
    autoExecute: false,
    userConfirmation: true,
    estimatedTime: 30,
    successRate: 95,
    execute: async (error: AppError, context?: RecoveryContext): Promise<RecoveryResult> => {
      // This strategy guides the user through recovery steps
      const recoverySteps = generateRecoverySteps(error, context);
      
      logUserAction('guided_recovery_started', { errorId: error.id });
      
      return {
        success: true,
        strategy: 'user_guided_recovery',
        message: 'Recovery assistance is available',
        nextSteps: recoverySteps
      };
    }
  }
];

// ============================================================================
// FALLBACK OPTIONS
// ============================================================================

const FALLBACK_OPTIONS: Record<string, FallbackOption> = {
  offline_mode: {
    id: 'offline_mode',
    name: 'Offline Mode',
    description: 'Continue working with limited functionality',
    limitations: [
      'Real-time data updates disabled',
      'Cannot save changes to server',
      'Limited to cached content'
    ],
    execute: async (context?: RecoveryContext) => {
      // Enable offline mode
      localStorage.setItem('offlineMode', 'true');
      return { mode: 'offline', features: ['view_cached_data', 'local_analysis'] };
    }
  },

  basic_mode: {
    id: 'basic_mode',
    name: 'Basic Mode',
    description: 'Simplified interface with essential features only',
    limitations: [
      'Advanced charts disabled',
      'Reduced real-time updates',
      'Limited indicators available'
    ],
    execute: async (context?: RecoveryContext) => {
      // Switch to basic mode
      localStorage.setItem('basicMode', 'true');
      return { mode: 'basic', features: ['basic_charts', 'simple_signals'] };
    }
  },

  read_only_mode: {
    id: 'read_only_mode',
    name: 'Read-Only Mode',
    description: 'View data without making changes',
    limitations: [
      'Cannot create or edit signals',
      'Settings changes disabled',
      'Portfolio updates paused'
    ],
    execute: async (context?: RecoveryContext) => {
      // Enable read-only mode
      localStorage.setItem('readOnlyMode', 'true');
      return { mode: 'read_only', features: ['view_data', 'view_charts'] };
    }
  }
};

// ============================================================================
// ERROR RECOVERY SERVICE CLASS
// ============================================================================

export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private recoveryHistory: Map<string, RecoveryResult[]> = new Map();
  private activeRecoveries: Set<string> = new Set();

  private constructor() {}

  public static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  public async attemptRecovery(
    error: AppError,
    context?: RecoveryContext
  ): Promise<RecoveryResult> {
    // Prevent multiple recovery attempts for the same error
    if (this.activeRecoveries.has(error.id)) {
      return {
        success: false,
        strategy: 'duplicate_prevention',
        message: 'Recovery already in progress for this error'
      };
    }

    this.activeRecoveries.add(error.id);

    try {
      // Get applicable strategies
      const strategies = this.getApplicableStrategies(error, context);
      
      // Try strategies in priority order
      for (const strategy of strategies) {
        if (strategy.userConfirmation && !await this.getUserConfirmation(strategy)) {
          continue;
        }

        const result = await strategy.execute(error, context);
        this.recordRecoveryAttempt(error.id, result);

        if (result.success) {
          return result;
        }
      }

      // If all strategies failed, offer fallback options
      return this.offerFallbackOptions(error, context);

    } finally {
      this.activeRecoveries.delete(error.id);
    }
  }

  public async executeFallback(
    fallbackId: string,
    context?: RecoveryContext
  ): Promise<any> {
    const fallback = FALLBACK_OPTIONS[fallbackId];
    
    if (!fallback) {
      throw new Error(`Unknown fallback option: ${fallbackId}`);
    }

    logUserAction('fallback_executed', { fallbackId, context });
    
    return await fallback.execute(context);
  }

  public getRecoveryHistory(errorId: string): RecoveryResult[] {
    return this.recoveryHistory.get(errorId) || [];
  }

  public getAvailableFallbacks(): FallbackOption[] {
    return Object.values(FALLBACK_OPTIONS);
  }

  private getApplicableStrategies(
    error: AppError,
    context?: RecoveryContext
  ): RecoveryStrategy[] {
    return RECOVERY_STRATEGIES
      .filter(strategy => strategy.applicableErrors.includes(error.type))
      .filter(strategy => !this.wasStrategyAttempted(error.id, strategy.id))
      .sort((a, b) => a.priority - b.priority);
  }

  private wasStrategyAttempted(errorId: string, strategyId: string): boolean {
    const history = this.recoveryHistory.get(errorId) || [];
    return history.some(result => result.strategy === strategyId);
  }

  private async getUserConfirmation(strategy: RecoveryStrategy): Promise<boolean> {
    // In a real implementation, this would show a user dialog
    // For now, we'll assume user consent for demonstration
    return new Promise(resolve => {
      const confirmed = window.confirm(
        `Would you like to try: ${strategy.name}?\n\n${strategy.description}\n\nEstimated time: ${strategy.estimatedTime} seconds`
      );
      resolve(confirmed);
    });
  }

  private recordRecoveryAttempt(errorId: string, result: RecoveryResult): void {
    if (!this.recoveryHistory.has(errorId)) {
      this.recoveryHistory.set(errorId, []);
    }
    
    this.recoveryHistory.get(errorId)!.push({
      ...result,
      timestamp: new Date().toISOString()
    } as any);
  }

  private offerFallbackOptions(
    error: AppError,
    context?: RecoveryContext
  ): RecoveryResult {
    const availableFallbacks = this.getRelevantFallbacks(error);
    
    return {
      success: false,
      strategy: 'fallback_options',
      message: 'Recovery strategies failed, but fallback options are available',
      nextSteps: availableFallbacks.map(fallback => 
        `Try ${fallback.name}: ${fallback.description}`
      ),
      fallbackAvailable: true
    };
  }

  private getRelevantFallbacks(error: AppError): FallbackOption[] {
    // Return fallbacks based on error type
    if (error.type === 'network') {
      return [FALLBACK_OPTIONS.offline_mode, FALLBACK_OPTIONS.basic_mode];
    }
    
    if (error.type === 'api') {
      return [FALLBACK_OPTIONS.basic_mode, FALLBACK_OPTIONS.read_only_mode];
    }
    
    return Object.values(FALLBACK_OPTIONS);
  }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

async function retryOriginalRequest(
  error: AppError,
  context?: RecoveryContext
): Promise<any> {
  // Extract original request details from error context
  const originalUrl = error.context?.url;
  const originalMethod = error.context?.method || 'GET';
  
  if (!originalUrl) {
    throw new Error('Cannot retry: original request details not available');
  }

  // Retry the original request
  switch (originalMethod.toUpperCase()) {
    case 'GET':
      return await api.get(originalUrl);
    case 'POST':
      return await api.post(originalUrl, error.context?.data);
    case 'PUT':
      return await api.put(originalUrl, error.context?.data);
    case 'DELETE':
      return await api.delete(originalUrl);
    default:
      throw new Error(`Unsupported method: ${originalMethod}`);
  }
}

async function tryAlternativeEndpoints(
  error: AppError,
  context?: RecoveryContext
): Promise<any> {
  // Define alternative endpoints for common requests
  const alternatives: Record<string, string[]> = {
    '/api/signals': ['/api/signals/cached', '/api/signals/backup'],
    '/api/market-data': ['/api/market-data/delayed', '/api/market-data/cached'],
    '/api/user/portfolio': ['/api/user/portfolio/snapshot', '/api/user/portfolio/cached']
  };

  const originalUrl = error.context?.url;
  if (!originalUrl) {
    throw new Error('No original URL available');
  }

  const alternativeUrls = alternatives[originalUrl] || [];
  
  for (const altUrl of alternativeUrls) {
    try {
      return await api.get(altUrl);
    } catch (altError) {
      continue; // Try next alternative
    }
  }

  throw new Error('All alternative endpoints failed');
}

function getCachedData(error: AppError, context?: RecoveryContext): any {
  const cacheKey = error.context?.cacheKey || error.context?.url;
  
  if (!cacheKey) {
    return null;
  }

  try {
    const cached = localStorage.getItem(`cache_${cacheKey}`);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      
      // Check if cache is not too old (24 hours)
      const isExpired = Date.now() - timestamp > 24 * 60 * 60 * 1000;
      
      return isExpired ? null : data;
    }
  } catch (cacheError) {
    console.warn('Failed to retrieve cached data:', cacheError);
  }

  return null;
}

function generateRecoverySteps(error: AppError, context?: RecoveryContext): string[] {
  const baseSteps = [
    'Check your internet connection',
    'Try refreshing the page',
    'Clear your browser cache',
    'Try again in a few minutes'
  ];

  // Add error-specific steps
  if (error.type === 'authentication') {
    return [
      'Click "Log In" to sign in again',
      'Check if your password has changed',
      'Clear browser cookies and try again',
      'Contact support if the problem persists'
    ];
  }

  if (error.type === 'validation') {
    return [
      'Check the highlighted fields for errors',
      'Make sure all required fields are filled',
      'Verify the format of dates and numbers',
      'Try submitting again'
    ];
  }

  return baseSteps;
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const errorRecoveryService = ErrorRecoveryService.getInstance();

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export async function attemptErrorRecovery(
  error: AppError,
  context?: RecoveryContext
): Promise<RecoveryResult> {
  return errorRecoveryService.attemptRecovery(error, context);
}

export async function executeFallback(
  fallbackId: string,
  context?: RecoveryContext
): Promise<any> {
  return errorRecoveryService.executeFallback(fallbackId, context);
}

export function getRecoveryHistory(errorId: string): RecoveryResult[] {
  return errorRecoveryService.getRecoveryHistory(errorId);
}
