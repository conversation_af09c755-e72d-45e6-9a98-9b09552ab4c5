/**
 * Enhanced Error Display Component
 *
 * Advanced error display that integrates user-friendly messages,
 * recovery options, and accessibility features for the best
 * possible error experience.
 *
 * Features:
 * - Context-aware error messages
 * - Multiple recovery strategies
 * - Progressive disclosure of information
 * - Accessibility compliance (WCAG 2.1)
 * - Keyboard navigation support
 * - Screen reader optimization
 * - Mobile-responsive design
 *
 * @version 1.0.0
 */

import React, { useState, useEffect, useRef } from 'react';
import { useError, AppError } from '../../context/ErrorContext';
import { getErrorMessage, setUserContext, ErrorMessage, ErrorAction } from '../../utils/errorMessages';
import { attemptErrorRecovery, executeFallback, RecoveryResult } from '../../services/errorRecoveryService';
import { logUserAction } from '../../utils/errorLogger';

// ============================================================================
// INTERFACES
// ============================================================================

interface EnhancedErrorDisplayProps {
  error: AppError | Error | string;
  context?: {
    feature?: string;
    component?: string;
    userJourney?: string;
  };
  variant?: 'full' | 'compact' | 'inline' | 'modal';
  showRecoveryOptions?: boolean;
  showTechnicalDetails?: boolean;
  onRecovered?: (result: RecoveryResult) => void;
  onDismiss?: () => void;
  className?: string;
}

interface RecoveryProgressProps {
  isRecovering: boolean;
  currentStrategy?: string;
  progress?: number;
  estimatedTime?: number;
}

// ============================================================================
// RECOVERY PROGRESS COMPONENT
// ============================================================================

const RecoveryProgress: React.FC<RecoveryProgressProps> = ({
  isRecovering,
  currentStrategy,
  progress = 0,
  estimatedTime = 0
}) => {
  const [timeRemaining, setTimeRemaining] = useState(estimatedTime);

  useEffect(() => {
    if (isRecovering && estimatedTime > 0) {
      const interval = setInterval(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isRecovering, estimatedTime]);

  if (!isRecovering) return null;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4" role="status" aria-live="polite">
      <div className="flex items-center mb-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
        <span className="text-sm font-medium text-blue-900">
          Attempting Recovery: {currentStrategy}
        </span>
      </div>

      {progress > 0 && (
        <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
            role="progressbar"
            aria-valuenow={progress}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </div>
      )}

      {timeRemaining > 0 && (
        <p className="text-xs text-blue-700">
          Estimated time remaining: {timeRemaining} seconds
        </p>
      )}
    </div>
  );
};

// ============================================================================
// ACTION BUTTON COMPONENT
// ============================================================================

const ActionButton: React.FC<{
  action: ErrorAction;
  onExecute: (actionType: string) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}> = ({ action, onExecute, disabled = false, size = 'md' }) => {
  const sizeClasses = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    link: 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 focus:ring-blue-500'
  };

  const handleClick = () => {
    logUserAction('error_action_clicked', { action: action.action, label: action.label });
    onExecute(action.action);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  };

  return (
    <button
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      className={`
        inline-flex items-center rounded-md font-medium transition-colors
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        ${sizeClasses[size]}
        ${variantClasses[action.type]}
      `}
      aria-label={`${action.label}${action.shortcut ? ` (${action.shortcut})` : ''}`}
      title={action.shortcut ? `Keyboard shortcut: ${action.shortcut}` : undefined}
    >
      {action.icon && (
        <span className="mr-2" aria-hidden="true">
          {getActionIcon(action.icon)}
        </span>
      )}
      {action.label}
      {action.shortcut && (
        <span className="ml-2 text-xs opacity-75" aria-hidden="true">
          {action.shortcut}
        </span>
      )}
    </button>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
  error,
  context,
  variant = 'full',
  showRecoveryOptions = true,
  showTechnicalDetails = false,
  onRecovered,
  onDismiss,
  className = ''
}) => {
  const { reportError } = useError();
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryProgress, setRecoveryProgress] = useState(0);
  const [currentStrategy, setCurrentStrategy] = useState<string>('');
  const [showDetails, setShowDetails] = useState(false);
  const [recoveryHistory, setRecoveryHistory] = useState<RecoveryResult[]>([]);

  const errorRef = useRef<HTMLDivElement>(null);
  const firstActionRef = useRef<HTMLButtonElement>(null);

  // Convert error to AppError if needed
  const appError = React.useMemo(() => {
    if (typeof error === 'string') {
      return {
        id: `error-${Date.now()}`,
        type: 'unknown' as const,
        category: 'medium' as const,
        message: error,
        userMessage: error
      } as AppError;
    } else if (error instanceof Error) {
      return {
        id: `error-${Date.now()}`,
        type: 'component' as const,
        category: 'high' as const,
        message: error.message,
        userMessage: error.message
      } as AppError;
    }
    return error;
  }, [error]);

  // Generate user-friendly error message
  useEffect(() => {
    if (context) {
      setUserContext(context);
    }

    const message = getErrorMessage(
      new Error(appError.message),
      appError.type,
      appError.category,
      context
    );

    setErrorMessage(message);
  }, [appError, context]);

  // Focus management for accessibility
  useEffect(() => {
    if (errorRef.current) {
      errorRef.current.focus();
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        handleAction('retry');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // ========================================================================
  // ACTION HANDLERS
  // ========================================================================

  const handleAction = async (actionType: string) => {
    logUserAction('error_action_executed', {
      actionType,
      errorId: appError.id,
      context
    });

    switch (actionType) {
      case 'retry':
        await handleRetry();
        break;
      case 'recover':
        await handleRecovery();
        break;
      case 'fallback':
        await handleFallback();
        break;
      case 'dismiss':
        onDismiss?.();
        break;
      case 'technical_details':
        setShowDetails(!showDetails);
        break;
      case 'support':
        window.open('/support', '_blank');
        break;
      case 'status_page':
        window.open('/status', '_blank');
        break;
      default:
        console.warn(`Unknown action type: ${actionType}`);
    }
  };

  const handleRetry = async () => {
    setIsRecovering(true);
    setCurrentStrategy('Manual Retry');
    setRecoveryProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setRecoveryProgress(prev => Math.min(prev + 20, 90));
      }, 500);

      // Attempt recovery
      const result = await attemptErrorRecovery(appError, {
        feature: context?.feature,
        component: context?.component
      });

      clearInterval(progressInterval);
      setRecoveryProgress(100);

      if (result.success) {
        onRecovered?.(result);
      } else {
        setRecoveryHistory(prev => [...prev, result]);
      }
    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);
    } finally {
      setIsRecovering(false);
      setRecoveryProgress(0);
    }
  };

  const handleRecovery = async () => {
    setIsRecovering(true);

    try {
      const result = await attemptErrorRecovery(appError, {
        feature: context?.feature,
        component: context?.component,
        userPreferences: {
          autoRetry: true,
          fallbackMode: false,
          dataPreservation: true
        }
      });

      setRecoveryHistory(prev => [...prev, result]);

      if (result.success) {
        onRecovered?.(result);
      }
    } finally {
      setIsRecovering(false);
    }
  };

  const handleFallback = async () => {
    try {
      await executeFallback('basic_mode', {
        feature: context?.feature,
        component: context?.component
      });

      // Notify parent component
      onRecovered?.({
        success: true,
        strategy: 'fallback',
        message: 'Switched to basic mode'
      });
    } catch (fallbackError) {
      console.error('Fallback failed:', fallbackError);
    }
  };

  // ========================================================================
  // RENDER METHODS
  // ========================================================================

  const renderErrorIcon = () => {
    const iconClass = variant === 'compact' ? 'w-5 h-5' : 'w-8 h-8';
    const colorClass = errorMessage?.severity === 'critical' ? 'text-red-500' :
                      errorMessage?.severity === 'high' ? 'text-orange-500' :
                      'text-yellow-500';

    return (
      <svg className={`${iconClass} ${colorClass}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    );
  };

  const renderInstructions = () => {
    if (!errorMessage?.instructions || variant === 'inline') return null;

    return (
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">What you can do:</h4>
        <ul className="text-sm text-gray-600 space-y-1" role="list">
          {errorMessage.instructions.map((instruction, index) => (
            <li key={index} className="flex items-start">
              <span className="text-blue-500 mr-2 mt-0.5" aria-hidden="true">•</span>
              <span>{instruction}</span>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  const renderActions = () => {
    if (!errorMessage?.actions || variant === 'inline') return null;

    return (
      <div className="mt-6 flex flex-wrap gap-3">
        {errorMessage.actions.map((action, index) => (
          <ActionButton
            key={action.action}
            action={action}
            onExecute={handleAction}
            disabled={isRecovering}
            size={variant === 'compact' ? 'sm' : 'md'}

          />
        ))}
      </div>
    );
  };

  const renderTechnicalDetails = () => {
    if (!showDetails || !showTechnicalDetails) return null;

    return (
      <details className="mt-4 p-3 bg-gray-50 rounded border">
        <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
          Technical Details
        </summary>
        <div className="text-xs font-mono text-gray-600 space-y-1">
          <div><strong>Error ID:</strong> {appError.id}</div>
          <div><strong>Type:</strong> {appError.type}</div>
          <div><strong>Category:</strong> {appError.category}</div>
          <div><strong>Message:</strong> {appError.message}</div>
          {appError.details && (
            <div><strong>Details:</strong> {JSON.stringify(appError.details, null, 2)}</div>
          )}
        </div>
      </details>
    );
  };

  const getContainerClasses = () => {
    const baseClasses = 'rounded-lg border';
    const severityClasses = errorMessage?.severity === 'critical' ? 'bg-red-50 border-red-200' :
                           errorMessage?.severity === 'high' ? 'bg-orange-50 border-orange-200' :
                           'bg-yellow-50 border-yellow-200';

    const variantClasses = {
      full: 'p-6',
      compact: 'p-4',
      inline: 'p-2 flex items-center space-x-2',
      modal: 'p-6 max-w-md mx-auto'
    };

    return `${baseClasses} ${severityClasses} ${variantClasses[variant]} ${className}`;
  };

  if (!errorMessage) {
    return <div>Loading error information...</div>;
  }

  // Inline variant
  if (variant === 'inline') {
    return (
      <div className={getContainerClasses()} role="alert" aria-live="assertive">
        {renderErrorIcon()}
        <span className="text-sm text-gray-800">{errorMessage.description}</span>
        <button
          onClick={() => handleAction('retry')}
          className="text-sm text-blue-600 hover:text-blue-800"
          disabled={isRecovering}
        >
          {isRecovering ? 'Retrying...' : 'Retry'}
        </button>
      </div>
    );
  }

  // Full/Compact/Modal variants
  return (
    <div
      ref={errorRef}
      className={getContainerClasses()}
      role="alert"
      aria-live="assertive"
      tabIndex={-1}
    >
      <RecoveryProgress
        isRecovering={isRecovering}
        currentStrategy={currentStrategy}
        progress={recoveryProgress}
      />

      <div className="flex items-start">
        <div className="flex-shrink-0">
          {renderErrorIcon()}
        </div>

        <div className="ml-3 flex-1">
          <h3 className="text-lg font-semibold text-gray-900">
            {errorMessage.title}
          </h3>

          <p className="mt-1 text-gray-600">
            {errorMessage.description}
          </p>

          {errorMessage.userImpact && (
            <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-blue-800">
              <strong>Impact:</strong> {errorMessage.userImpact}
            </div>
          )}

          {renderInstructions()}
          {renderActions()}
          {renderTechnicalDetails()}

          {recoveryHistory.length > 0 && (
            <div className="mt-4 text-xs text-gray-500">
              Recovery attempts: {recoveryHistory.length}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function getActionIcon(iconName: string): React.ReactNode {
  const iconClass = "w-4 h-4";

  switch (iconName) {
    case 'refresh':
      return (
        <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      );
    case 'help':
      return (
        <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    case 'external':
      return (
        <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
      );
    default:
      return null;
  }
}

export default EnhancedErrorDisplay;
