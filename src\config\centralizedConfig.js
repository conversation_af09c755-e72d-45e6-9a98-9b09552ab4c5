/**
 * Centralized Configuration Service
 *
 * Consolidates all configuration settings across the application with:
 * - Environment variable validation using Joi
 * - Type-safe configuration access
 * - Default values and fallbacks
 * - Configuration hot-reloading support
 * - Comprehensive logging of configuration issues
 *
 * @version 1.0.0
 */

import Joi from 'joi';
import dotenv from 'dotenv';
import logger from '../utils/logger.js';

// Load environment variables
dotenv.config();

// ============================================================================
// ENVIRONMENT VALIDATION SCHEMA
// ============================================================================

const envSchema = Joi.object({
  // Application Settings
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().port().default(3000),

  // Security
  JWT_SECRET: Joi.string().min(32).required(),
  CORS_ORIGIN: Joi.string().default('*'),

  // Database Configuration
  MONGODB_URI: Joi.string().uri().default('mongodb://localhost:27017/trading-signals'),
  DB_MIN_POOL_SIZE: Joi.number().min(1).default(5),
  DB_MAX_POOL_SIZE: Joi.number().min(5).default(20),
  DB_SLOW_QUERY_THRESHOLD: Joi.number().min(50).default(100),
  DB_CRITICAL_QUERY_THRESHOLD: Joi.number().min(100).default(500),

  // Redis Configuration
  USE_REDIS: Joi.boolean().default(false),
  REDIS_URL: Joi.string().uri().default('redis://localhost:6379'),
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().port().default(6379),
  REDIS_PASSWORD: Joi.string().optional(),
  REDIS_DB: Joi.number().min(0).max(15).default(0),

  // Cache TTL Settings (in seconds)
  CACHE_TTL: Joi.number().min(60).default(3600),
  CACHE_TTL_MARKET_DATA: Joi.number().min(10).default(30),
  CACHE_TTL_TECHNICAL_INDICATORS: Joi.number().min(60).default(300),
  CACHE_TTL_AI_ANALYSIS: Joi.number().min(300).default(600),
  CACHE_TTL_USER_SESSIONS: Joi.number().min(3600).default(86400),
  CACHE_TTL_NEWS_SENTIMENT: Joi.number().min(300).default(1800),

  // API Keys
  ALPHA_VANTAGE_API_KEY: Joi.string().required(),
  FRED_API_KEY: Joi.string().required(),
  POLYGON_API_KEY: Joi.string().required(),
  FINNHUB_API_KEY: Joi.string().required(),
  TWELVE_DATA_API_KEY: Joi.string().optional(),
  OPENAI_API_KEY: Joi.string().optional(),
  FMP_API_KEY: Joi.string().optional(),

  // Payment Processing
  STRIPE_SECRET_KEY: Joi.string().optional(),
  STRIPE_PUBLISHABLE_KEY: Joi.string().optional(),
  STRIPE_WEBHOOK_SECRET: Joi.string().optional(),

  // Monitoring & Logging
  SENTRY_DSN: Joi.string().uri().optional(),
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),

  // Circuit Breaker Configuration
  AI_CIRCUIT_BREAKER_FAILURE_THRESHOLD: Joi.number().min(1).default(5),
  AI_CIRCUIT_BREAKER_RECOVERY_TIMEOUT: Joi.number().min(10000).default(60000),
  API_CIRCUIT_BREAKER_FAILURE_THRESHOLD: Joi.number().min(1).default(3),
  API_CIRCUIT_BREAKER_RECOVERY_TIMEOUT: Joi.number().min(5000).default(30000),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().min(60000).default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().min(10).default(100),

  // Performance Monitoring
  ENABLE_PERFORMANCE_MONITORING: Joi.boolean().default(true),
  PERFORMANCE_SAMPLE_RATE: Joi.number().min(0).max(1).default(0.1),
}).unknown(true); // Allow additional environment variables

// ============================================================================
// CONFIGURATION CLASS
// ============================================================================

class CentralizedConfig {
  constructor() {
    this.config = null;
    this.isInitialized = false;
    this.validationErrors = [];
    this.initialize();
  }

  /**
   * Initialize configuration with validation
   */
  initialize() {
    try {
      const { error, value } = envSchema.validate(process.env, {
        allowUnknown: true,
        stripUnknown: false
      });

      if (error) {
        this.validationErrors = error.details;
        logger.error('Configuration validation failed:', {
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
            value: detail.context?.value
          }))
        });

        // Use default values where possible
        this.config = this.createDefaultConfig();
      } else {
        this.config = this.createConfigFromValidatedEnv(value);
      }

      this.isInitialized = true;
      this.logConfigurationStatus();

    } catch (error) {
      logger.error('Failed to initialize configuration:', error);
      this.config = this.createDefaultConfig();
      this.isInitialized = true;
    }
  }

  /**
   * Create configuration object from validated environment variables
   */
  createConfigFromValidatedEnv(env) {
    return {
      // Application Settings
      app: {
        env: env.NODE_ENV,
        port: env.PORT,
        isDevelopment: env.NODE_ENV === 'development',
        isProduction: env.NODE_ENV === 'production',
        isTest: env.NODE_ENV === 'test'
      },

      // Security Configuration
      security: {
        jwt: {
          secret: env.JWT_SECRET,
          expiresIn: '24h',
          refreshExpiresIn: '7d'
        },
        cors: {
          origin: env.CORS_ORIGIN,
          credentials: true
        },
        rateLimiting: {
          windowMs: env.RATE_LIMIT_WINDOW_MS,
          maxRequestsPerWindow: env.RATE_LIMIT_MAX_REQUESTS,
          skipSuccessfulRequests: false,
          skipFailedRequests: false
        }
      },

      // Database Configuration
      database: {
        mongodb: {
          uri: env.MONGODB_URI,
          options: {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            minPoolSize: env.DB_MIN_POOL_SIZE,
            maxPoolSize: env.DB_MAX_POOL_SIZE,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000,
            socketTimeoutMS: 45000,
            maxIdleTimeMS: 30000
          },
          performance: {
            slowQueryThreshold: env.DB_SLOW_QUERY_THRESHOLD,
            criticalQueryThreshold: env.DB_CRITICAL_QUERY_THRESHOLD,
            enableProfiling: env.NODE_ENV !== 'production'
          }
        }
      },

      // Cache Configuration
      cache: {
        redis: {
          enabled: env.USE_REDIS,
          url: env.REDIS_URL,
          host: env.REDIS_HOST,
          port: env.REDIS_PORT,
          password: env.REDIS_PASSWORD,
          db: env.REDIS_DB,
          retryStrategy: (times) => Math.min(times * 50, 2000),
          lazyConnect: true,
          maxRetriesPerRequest: 3,
          connectTimeout: 5000,
          commandTimeout: 3000,
          enableReadyCheck: true,
          maxMemoryPolicy: 'allkeys-lru'
        },
        nodeCache: {
          stdTTL: env.CACHE_TTL,
          checkperiod: 120,
          useClones: false,
          deleteOnExpire: true
        },
        ttl: {
          default: env.CACHE_TTL,
          marketData: env.CACHE_TTL_MARKET_DATA,
          technicalIndicators: env.CACHE_TTL_TECHNICAL_INDICATORS,
          aiAnalysis: env.CACHE_TTL_AI_ANALYSIS,
          userSessions: env.CACHE_TTL_USER_SESSIONS,
          newsSentiment: env.CACHE_TTL_NEWS_SENTIMENT
        }
      },

      // API Configuration
      apis: {
        alphaVantage: {
          baseUrl: 'https://www.alphavantage.co/query',
          apiKey: env.ALPHA_VANTAGE_API_KEY,
          timeout: 30000,
          retries: 3
        },
        fred: {
          baseUrl: 'https://api.stlouisfed.org/fred',
          apiKey: env.FRED_API_KEY,
          timeout: 30000,
          retries: 3,
          endpoints: {
            series: '/series/observations',
            releases: '/releases',
            calendar: '/releases/dates'
          }
        },
        polygon: {
          baseUrl: 'https://api.polygon.io',
          apiKey: env.POLYGON_API_KEY,
          timeout: 30000,
          retries: 3
        },
        finnhub: {
          baseUrl: 'https://finnhub.io/api/v1',
          apiKey: env.FINNHUB_API_KEY,
          timeout: 30000,
          retries: 3
        },
        twelveData: {
          baseUrl: 'https://api.twelvedata.com',
          apiKey: env.TWELVE_DATA_API_KEY || 'demo',
          timeout: 30000,
          retries: 3
        },
        openai: {
          baseUrl: 'https://api.openai.com/v1',
          apiKey: env.OPENAI_API_KEY,
          timeout: 60000,
          retries: 2,
          models: {
            default: 'gpt-3.5-turbo',
            advanced: 'gpt-4',
            fallback: 'gpt-3.5-turbo'
          }
        },
        fmp: {
          baseUrl: 'https://financialmodelingprep.com/api/v3',
          apiKey: env.FMP_API_KEY,
          timeout: 30000,
          retries: 3
        }
      },

      // Circuit Breaker Configuration
      circuitBreaker: {
        ai: {
          failureThreshold: env.AI_CIRCUIT_BREAKER_FAILURE_THRESHOLD,
          recoveryTimeout: env.AI_CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
          monitoringPeriod: 60000
        },
        api: {
          failureThreshold: env.API_CIRCUIT_BREAKER_FAILURE_THRESHOLD,
          recoveryTimeout: env.API_CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
          monitoringPeriod: 30000
        }
      },

      // Payment Processing
      payments: {
        stripe: {
          secretKey: env.STRIPE_SECRET_KEY,
          publishableKey: env.STRIPE_PUBLISHABLE_KEY,
          webhookSecret: env.STRIPE_WEBHOOK_SECRET,
          enabled: !!(env.STRIPE_SECRET_KEY && env.STRIPE_PUBLISHABLE_KEY)
        }
      },

      // Monitoring & Logging
      monitoring: {
        sentry: {
          dsn: env.SENTRY_DSN,
          enabled: !!env.SENTRY_DSN,
          environment: env.NODE_ENV,
          sampleRate: env.PERFORMANCE_SAMPLE_RATE
        },
        logging: {
          level: env.LOG_LEVEL,
          format: env.NODE_ENV === 'production' ? 'json' : 'dev',
          fileRotation: {
            maxFiles: '30d',
            maxSize: '20m',
            datePattern: 'YYYY-MM-DD'
          },
          console: {
            enabled: env.NODE_ENV !== 'production'
          }
        },
        performance: {
          enabled: env.ENABLE_PERFORMANCE_MONITORING,
          sampleRate: env.PERFORMANCE_SAMPLE_RATE,
          slowRequestThreshold: 1000,
          criticalRequestThreshold: 5000
        }
      }
    };
  }

  /**
   * Create default configuration for fallback scenarios
   */
  createDefaultConfig() {
    logger.warn('Using default configuration due to validation errors');

    return {
      app: {
        env: 'development',
        port: 3000,
        isDevelopment: true,
        isProduction: false,
        isTest: false
      },
      security: {
        jwt: {
          secret: 'default-secret-change-in-production',
          expiresIn: '24h',
          refreshExpiresIn: '7d'
        },
        cors: {
          origin: '*',
          credentials: true
        },
        rateLimiting: {
          windowMs: 900000,
          maxRequestsPerWindow: 100,
          skipSuccessfulRequests: false,
          skipFailedRequests: false
        }
      },
      database: {
        mongodb: {
          uri: 'mongodb://localhost:27017/trading-signals',
          options: {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            minPoolSize: 5,
            maxPoolSize: 20,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
          },
          performance: {
            slowQueryThreshold: 100,
            criticalQueryThreshold: 500,
            enableProfiling: true
          }
        }
      },
      cache: {
        redis: {
          enabled: false,
          url: 'redis://localhost:6379',
          host: 'localhost',
          port: 6379,
          db: 0
        },
        nodeCache: {
          stdTTL: 3600,
          checkperiod: 120
        },
        ttl: {
          default: 3600,
          marketData: 30,
          technicalIndicators: 300,
          aiAnalysis: 600,
          userSessions: 86400,
          newsSentiment: 1800
        }
      },
      apis: {
        // Default API configurations with placeholder keys
        alphaVantage: { baseUrl: 'https://www.alphavantage.co/query', apiKey: 'demo' },
        fred: { baseUrl: 'https://api.stlouisfed.org/fred', apiKey: 'demo' },
        polygon: { baseUrl: 'https://api.polygon.io', apiKey: 'demo' },
        finnhub: { baseUrl: 'https://finnhub.io/api/v1', apiKey: 'demo' }
      },
      monitoring: {
        logging: {
          level: 'info',
          format: 'dev',
          console: { enabled: true }
        }
      }
    };
  }

  /**
   * Log configuration status and any issues
   */
  logConfigurationStatus() {
    const status = {
      initialized: this.isInitialized,
      hasValidationErrors: this.validationErrors.length > 0,
      environment: this.config.app.env,
      redisEnabled: this.config.cache.redis.enabled,
      apiKeysConfigured: this.getConfiguredApiKeys()
    };

    if (this.validationErrors.length > 0) {
      logger.warn('Configuration initialized with validation errors:', {
        status,
        errors: this.validationErrors.map(err => err.message)
      });
    } else {
      logger.info('Configuration initialized successfully:', status);
    }
  }

  /**
   * Get list of configured API keys (without exposing the actual keys)
   */
  getConfiguredApiKeys() {
    const apis = this.config.apis;
    const configured = [];

    Object.keys(apis).forEach(apiName => {
      if (apis[apiName].apiKey && apis[apiName].apiKey !== 'demo') {
        configured.push(apiName);
      }
    });

    return configured;
  }

  /**
   * Get configuration value by path (e.g., 'database.mongodb.uri')
   */
  get(path, defaultValue = undefined) {
    if (!this.isInitialized) {
      logger.warn('Configuration not initialized, returning default value');
      return defaultValue;
    }

    const keys = path.split('.');
    let value = this.config;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * Check if configuration is valid
   */
  isValid() {
    return this.isInitialized && this.validationErrors.length === 0;
  }

  /**
   * Get all configuration
   */
  getAll() {
    return this.config;
  }

  /**
   * Reload configuration (useful for hot-reloading)
   */
  reload() {
    logger.info('Reloading configuration...');
    this.initialize();
  }

  /**
   * Validate specific configuration section
   */
  validateSection(sectionName) {
    const section = this.get(sectionName);
    if (!section) {
      return { valid: false, error: `Section '${sectionName}' not found` };
    }

    // Add specific validation logic for different sections
    switch (sectionName) {
      case 'database.mongodb':
        return this.validateMongoDBConfig(section);
      case 'cache.redis':
        return this.validateRedisConfig(section);
      case 'apis':
        return this.validateApiConfig(section);
      default:
        return { valid: true };
    }
  }

  /**
   * Validate MongoDB configuration
   */
  validateMongoDBConfig(config) {
    if (!config.uri) {
      return { valid: false, error: 'MongoDB URI is required' };
    }
    return { valid: true };
  }

  /**
   * Validate Redis configuration
   */
  validateRedisConfig(config) {
    if (config.enabled && (!config.host || !config.port)) {
      return { valid: false, error: 'Redis host and port are required when enabled' };
    }
    return { valid: true };
  }

  /**
   * Validate API configuration
   */
  validateApiConfig(config) {
    const requiredApis = ['alphaVantage', 'fred', 'polygon', 'finnhub'];
    const missingApis = requiredApis.filter(api =>
      !config[api] || !config[api].apiKey || config[api].apiKey === 'demo'
    );

    if (missingApis.length > 0) {
      return {
        valid: false,
        error: `Missing API keys for: ${missingApis.join(', ')}`,
        missingApis
      };
    }
    return { valid: true };
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const centralizedConfig = new CentralizedConfig();

export default centralizedConfig;
export { CentralizedConfig };
