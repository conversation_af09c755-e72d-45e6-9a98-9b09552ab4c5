/**
 * Enhanced Error Handling Service
 * 
 * Provides comprehensive error handling with:
 * - Standardized error categorization and user-friendly messages
 * - Integration with existing APIError class
 * - Automatic error reporting and logging
 * - Context-aware error recovery suggestions
 * - Performance impact tracking
 * 
 * @version 1.0.0
 */

import { APIError } from '../middleware/errorHandler.js';
import logger from '../utils/logger.js';
import centralizedConfig from '../config/centralizedConfig.js';

// ============================================================================
// ERROR CATEGORIES AND MAPPINGS
// ============================================================================

const ERROR_CATEGORIES = {
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  NOT_FOUND: 'not_found',
  RATE_LIMIT: 'rate_limit',
  NETWORK: 'network',
  DATABASE: 'database',
  CACHE: 'cache',
  API_EXTERNAL: 'api_external',
  AI_SERVICE: 'ai_service',
  BUSINESS_LOGIC: 'business_logic',
  SYSTEM: 'system',
  UNKNOWN: 'unknown'
};

const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

const USER_FRIENDLY_MESSAGES = {
  [ERROR_CATEGORIES.VALIDATION]: {
    title: 'Invalid Input',
    message: 'Please check your input and try again.',
    suggestion: 'Make sure all required fields are filled correctly.'
  },
  [ERROR_CATEGORIES.AUTHENTICATION]: {
    title: 'Authentication Required',
    message: 'Please log in to access this feature.',
    suggestion: 'Check your credentials and try logging in again.'
  },
  [ERROR_CATEGORIES.AUTHORIZATION]: {
    title: 'Access Denied',
    message: 'You don\'t have permission to perform this action.',
    suggestion: 'Contact your administrator if you believe this is an error.'
  },
  [ERROR_CATEGORIES.NOT_FOUND]: {
    title: 'Not Found',
    message: 'The requested resource could not be found.',
    suggestion: 'Please check the URL or try searching for what you need.'
  },
  [ERROR_CATEGORIES.RATE_LIMIT]: {
    title: 'Too Many Requests',
    message: 'You\'ve made too many requests. Please wait before trying again.',
    suggestion: 'Try again in a few minutes.'
  },
  [ERROR_CATEGORIES.NETWORK]: {
    title: 'Connection Problem',
    message: 'Unable to connect to our servers.',
    suggestion: 'Check your internet connection and try again.'
  },
  [ERROR_CATEGORIES.DATABASE]: {
    title: 'Data Access Error',
    message: 'We\'re having trouble accessing your data.',
    suggestion: 'Please try again in a moment. If the problem persists, contact support.'
  },
  [ERROR_CATEGORIES.CACHE]: {
    title: 'Temporary Issue',
    message: 'We\'re experiencing a temporary issue.',
    suggestion: 'Please refresh the page and try again.'
  },
  [ERROR_CATEGORIES.API_EXTERNAL]: {
    title: 'External Service Error',
    message: 'One of our data providers is currently unavailable.',
    suggestion: 'Please try again later. Market data may be delayed.'
  },
  [ERROR_CATEGORIES.AI_SERVICE]: {
    title: 'Analysis Unavailable',
    message: 'AI analysis is temporarily unavailable.',
    suggestion: 'Please try again in a few minutes or use manual analysis tools.'
  },
  [ERROR_CATEGORIES.BUSINESS_LOGIC]: {
    title: 'Operation Not Allowed',
    message: 'This operation cannot be completed.',
    suggestion: 'Please review the requirements and try again.'
  },
  [ERROR_CATEGORIES.SYSTEM]: {
    title: 'System Error',
    message: 'We\'re experiencing technical difficulties.',
    suggestion: 'Our team has been notified. Please try again later.'
  },
  [ERROR_CATEGORIES.UNKNOWN]: {
    title: 'Unexpected Error',
    message: 'Something unexpected happened.',
    suggestion: 'Please try again. If the problem continues, contact support.'
  }
};

// ============================================================================
// ENHANCED ERROR HANDLING SERVICE
// ============================================================================

class EnhancedErrorHandlingService {
  constructor() {
    this.errorStats = new Map();
    this.recentErrors = [];
    this.maxRecentErrors = 100;
    this.config = centralizedConfig.getAll();
  }

  /**
   * Create standardized API error with enhanced context
   */
  createAPIError(error, context = {}) {
    const category = this.categorizeError(error, context);
    const severity = this.determineSeverity(error, category);
    const userMessage = this.getUserFriendlyMessage(category, error);
    const statusCode = this.getStatusCodeForCategory(category);
    const errorCode = this.getErrorCodeForCategory(category);

    // Track error statistics
    this.trackError(category, severity, context);

    // Create enhanced APIError
    const apiError = new APIError(
      statusCode,
      userMessage.message,
      true, // isOperational
      context.requestId,
      errorCode,
      this.sanitizeErrorDetails(error, context),
      userMessage.suggestion
    );

    // Add enhanced properties
    apiError.category = category;
    apiError.severity = severity;
    apiError.userTitle = userMessage.title;
    apiError.context = this.sanitizeContext(context);
    apiError.originalError = error.message;

    // Log the error with appropriate level
    this.logError(apiError, error, context);

    return apiError;
  }

  /**
   * Categorize error based on type and context
   */
  categorizeError(error, context = {}) {
    const errorMessage = error.message?.toLowerCase() || '';
    const errorName = error.name?.toLowerCase() || '';

    // Check context first
    if (context.category) {
      return context.category;
    }

    // Database errors
    if (errorName.includes('mongo') || errorMessage.includes('database') || 
        errorMessage.includes('connection') && context.service === 'database') {
      return ERROR_CATEGORIES.DATABASE;
    }

    // Cache errors
    if (errorMessage.includes('redis') || errorMessage.includes('cache')) {
      return ERROR_CATEGORIES.CACHE;
    }

    // Network errors
    if (errorMessage.includes('network') || errorMessage.includes('fetch') ||
        errorMessage.includes('timeout') || errorMessage.includes('econnrefused')) {
      return ERROR_CATEGORIES.NETWORK;
    }

    // Authentication errors
    if (error.status === 401 || errorMessage.includes('unauthorized') ||
        errorMessage.includes('authentication')) {
      return ERROR_CATEGORIES.AUTHENTICATION;
    }

    // Authorization errors
    if (error.status === 403 || errorMessage.includes('forbidden') ||
        errorMessage.includes('permission')) {
      return ERROR_CATEGORIES.AUTHORIZATION;
    }

    // Validation errors
    if (error.status === 400 || errorMessage.includes('validation') ||
        errorMessage.includes('invalid') || errorName.includes('validation')) {
      return ERROR_CATEGORIES.VALIDATION;
    }

    // Not found errors
    if (error.status === 404 || errorMessage.includes('not found')) {
      return ERROR_CATEGORIES.NOT_FOUND;
    }

    // Rate limiting errors
    if (error.status === 429 || errorMessage.includes('rate limit') ||
        errorMessage.includes('too many requests')) {
      return ERROR_CATEGORIES.RATE_LIMIT;
    }

    // AI service errors
    if (context.service === 'ai' || errorMessage.includes('openai') ||
        errorMessage.includes('ai analysis')) {
      return ERROR_CATEGORIES.AI_SERVICE;
    }

    // External API errors
    if (context.service === 'external_api' || 
        ['alphaVantage', 'polygon', 'finnhub', 'fred'].includes(context.provider)) {
      return ERROR_CATEGORIES.API_EXTERNAL;
    }

    // System errors (5xx status codes)
    if (error.status >= 500) {
      return ERROR_CATEGORIES.SYSTEM;
    }

    return ERROR_CATEGORIES.UNKNOWN;
  }

  /**
   * Determine error severity
   */
  determineSeverity(error, category) {
    // Critical errors that affect core functionality
    if (category === ERROR_CATEGORIES.DATABASE || 
        category === ERROR_CATEGORIES.SYSTEM ||
        error.status >= 500) {
      return ERROR_SEVERITY.CRITICAL;
    }

    // High severity errors that significantly impact user experience
    if (category === ERROR_CATEGORIES.AUTHENTICATION ||
        category === ERROR_CATEGORIES.AI_SERVICE ||
        category === ERROR_CATEGORIES.API_EXTERNAL) {
      return ERROR_SEVERITY.HIGH;
    }

    // Medium severity errors that partially impact functionality
    if (category === ERROR_CATEGORIES.CACHE ||
        category === ERROR_CATEGORIES.NETWORK ||
        category === ERROR_CATEGORIES.BUSINESS_LOGIC) {
      return ERROR_SEVERITY.MEDIUM;
    }

    // Low severity errors that don't significantly impact functionality
    return ERROR_SEVERITY.LOW;
  }

  /**
   * Get user-friendly message for error category
   */
  getUserFriendlyMessage(category, error) {
    const baseMessage = USER_FRIENDLY_MESSAGES[category] || USER_FRIENDLY_MESSAGES[ERROR_CATEGORIES.UNKNOWN];
    
    // Customize message based on specific error details
    if (category === ERROR_CATEGORIES.RATE_LIMIT && error.retryAfter) {
      return {
        ...baseMessage,
        suggestion: `Please wait ${error.retryAfter} seconds before trying again.`
      };
    }

    if (category === ERROR_CATEGORIES.API_EXTERNAL && error.provider) {
      return {
        ...baseMessage,
        message: `Our ${error.provider} data provider is currently unavailable.`
      };
    }

    return baseMessage;
  }

  /**
   * Get HTTP status code for error category
   */
  getStatusCodeForCategory(category) {
    const statusMap = {
      [ERROR_CATEGORIES.VALIDATION]: 400,
      [ERROR_CATEGORIES.AUTHENTICATION]: 401,
      [ERROR_CATEGORIES.AUTHORIZATION]: 403,
      [ERROR_CATEGORIES.NOT_FOUND]: 404,
      [ERROR_CATEGORIES.RATE_LIMIT]: 429,
      [ERROR_CATEGORIES.NETWORK]: 503,
      [ERROR_CATEGORIES.DATABASE]: 503,
      [ERROR_CATEGORIES.CACHE]: 503,
      [ERROR_CATEGORIES.API_EXTERNAL]: 503,
      [ERROR_CATEGORIES.AI_SERVICE]: 503,
      [ERROR_CATEGORIES.BUSINESS_LOGIC]: 422,
      [ERROR_CATEGORIES.SYSTEM]: 500,
      [ERROR_CATEGORIES.UNKNOWN]: 500
    };

    return statusMap[category] || 500;
  }

  /**
   * Get error code for category
   */
  getErrorCodeForCategory(category) {
    return category.toUpperCase().replace('_', '_ERROR_');
  }

  /**
   * Sanitize error details for client response
   */
  sanitizeErrorDetails(error, context) {
    const details = {
      timestamp: new Date().toISOString(),
      category: this.categorizeError(error, context)
    };

    // Add safe error details in development
    if (this.config.app.isDevelopment) {
      details.originalMessage = error.message;
      details.stack = error.stack?.split('\n').slice(0, 5); // Limit stack trace
    }

    // Add context information
    if (context.endpoint) details.endpoint = context.endpoint;
    if (context.method) details.method = context.method;
    if (context.userId) details.userId = context.userId;

    return details;
  }

  /**
   * Sanitize context for logging
   */
  sanitizeContext(context) {
    const sanitized = { ...context };
    
    // Remove sensitive information
    delete sanitized.password;
    delete sanitized.token;
    delete sanitized.apiKey;
    delete sanitized.secret;

    return sanitized;
  }

  /**
   * Track error statistics
   */
  trackError(category, severity, context) {
    const key = `${category}_${severity}`;
    const current = this.errorStats.get(key) || { count: 0, lastOccurrence: null };
    
    this.errorStats.set(key, {
      count: current.count + 1,
      lastOccurrence: new Date().toISOString(),
      category,
      severity
    });

    // Add to recent errors
    this.recentErrors.unshift({
      category,
      severity,
      timestamp: new Date().toISOString(),
      context: this.sanitizeContext(context)
    });

    // Limit recent errors array size
    if (this.recentErrors.length > this.maxRecentErrors) {
      this.recentErrors = this.recentErrors.slice(0, this.maxRecentErrors);
    }
  }

  /**
   * Log error with appropriate level
   */
  logError(apiError, originalError, context) {
    const logData = {
      errorId: apiError.requestId,
      category: apiError.category,
      severity: apiError.severity,
      statusCode: apiError.statusCode,
      message: apiError.message,
      originalMessage: originalError.message,
      context: this.sanitizeContext(context),
      timestamp: apiError.timestamp
    };

    switch (apiError.severity) {
      case ERROR_SEVERITY.CRITICAL:
        logger.error('Critical error occurred', logData);
        break;
      case ERROR_SEVERITY.HIGH:
        logger.error('High severity error', logData);
        break;
      case ERROR_SEVERITY.MEDIUM:
        logger.warn('Medium severity error', logData);
        break;
      default:
        logger.info('Low severity error', logData);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    return {
      totalErrors: Array.from(this.errorStats.values()).reduce((sum, stat) => sum + stat.count, 0),
      errorsByCategory: Object.fromEntries(this.errorStats),
      recentErrors: this.recentErrors.slice(0, 10), // Last 10 errors
      topErrorCategories: this.getTopErrorCategories()
    };
  }

  /**
   * Get top error categories by frequency
   */
  getTopErrorCategories() {
    return Array.from(this.errorStats.entries())
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([key, stats]) => ({
        category: stats.category,
        severity: stats.severity,
        count: stats.count,
        lastOccurrence: stats.lastOccurrence
      }));
  }

  /**
   * Clear error statistics (useful for testing or periodic cleanup)
   */
  clearStats() {
    this.errorStats.clear();
    this.recentErrors = [];
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const enhancedErrorHandlingService = new EnhancedErrorHandlingService();

export default enhancedErrorHandlingService;
export { EnhancedErrorHandlingService, ERROR_CATEGORIES, ERROR_SEVERITY };
