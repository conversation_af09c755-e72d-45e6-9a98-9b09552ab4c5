/**
 * Comprehensive Database Service
 * 
 * Provides optimized database operations with:
 * - Intelligent indexing strategies
 * - Aggressive caching with Redis integration
 * - Query performance monitoring
 * - Connection pool optimization
 * - Automatic failover and recovery
 * 
 * @version 1.0.0
 */

import mongoose from 'mongoose';
import centralizedConfig from '../config/centralizedConfig.js';
import enhancedErrorHandlingService from './enhancedErrorHandlingService.js';
import logger from '../utils/logger.js';

// ============================================================================
// DATABASE OPTIMIZATION INDEXES
// ============================================================================

const OPTIMIZED_INDEXES = {
  // Trading Signals Collection
  signals: [
    { symbol: 1, timestamp: -1 }, // Most common query pattern
    { timestamp: -1, confidence: -1 }, // Recent high-confidence signals
    { userId: 1, timestamp: -1 }, // User-specific signals
    { symbol: 1, signalType: 1, timestamp: -1 }, // Signal type filtering
    { confidence: -1, timestamp: -1 }, // High confidence signals
    { 'metadata.source': 1, timestamp: -1 }, // Source-based queries
    { status: 1, timestamp: -1 }, // Active/inactive signals
    // Compound index for complex queries
    { symbol: 1, signalType: 1, confidence: -1, timestamp: -1 }
  ],

  // Market Data Collection
  marketData: [
    { symbol: 1, timestamp: -1 }, // Primary query pattern
    { timestamp: -1 }, // Time-based queries
    { symbol: 1, interval: 1, timestamp: -1 }, // Interval-specific data
    { 'metadata.source': 1, symbol: 1, timestamp: -1 }, // Source tracking
    // TTL index for automatic cleanup
    { timestamp: 1, expireAfterSeconds: 86400 * 30 } // 30 days retention
  ],

  // Technical Indicators Collection
  technicalIndicators: [
    { symbol: 1, indicator: 1, timestamp: -1 }, // Indicator queries
    { symbol: 1, timestamp: -1 }, // Symbol-based queries
    { timestamp: -1 }, // Time-based cleanup
    // TTL index for automatic cleanup
    { timestamp: 1, expireAfterSeconds: 86400 * 7 } // 7 days retention
  ],

  // User Collection
  users: [
    { email: 1 }, // Unique index for login
    { username: 1 }, // Unique index for username
    { 'subscription.plan': 1, 'subscription.status': 1 }, // Subscription queries
    { createdAt: -1 }, // Registration tracking
    { lastLoginAt: -1 }, // Activity tracking
    { 'preferences.symbols': 1 } // Watchlist queries
  ],

  // User Sessions Collection
  userSessions: [
    { userId: 1, isActive: 1 }, // Active sessions
    { sessionId: 1 }, // Unique session lookup
    { expiresAt: 1 }, // Session cleanup
    // TTL index for automatic cleanup
    { expiresAt: 1, expireAfterSeconds: 0 }
  ],

  // Notifications Collection
  notifications: [
    { userId: 1, createdAt: -1 }, // User notifications
    { userId: 1, read: 1, createdAt: -1 }, // Unread notifications
    { type: 1, createdAt: -1 }, // Notification type queries
    // TTL index for automatic cleanup
    { createdAt: 1, expireAfterSeconds: 86400 * 90 } // 90 days retention
  ],

  // Performance Logs Collection
  performanceLogs: [
    { timestamp: -1 }, // Time-based queries
    { operation: 1, timestamp: -1 }, // Operation tracking
    { duration: -1, timestamp: -1 }, // Slow query identification
    // TTL index for automatic cleanup
    { timestamp: 1, expireAfterSeconds: 86400 * 14 } // 14 days retention
  ]
};

// ============================================================================
// COMPREHENSIVE DATABASE SERVICE
// ============================================================================

class ComprehensiveDatabaseService {
  constructor() {
    this.config = centralizedConfig.get('database.mongodb');
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;
    this.queryStats = new Map();
    this.slowQueries = [];
    this.connectionPool = null;
    this.healthMetrics = {
      totalQueries: 0,
      slowQueries: 0,
      failedQueries: 0,
      averageResponseTime: 0,
      connectionPoolStats: {}
    };
  }

  /**
   * Initialize database connection with optimization
   */
  async initialize() {
    try {
      logger.info('Initializing comprehensive database service...');

      // Configure mongoose with optimized settings
      mongoose.set('strictQuery', false);
      
      // Set up connection event handlers
      this.setupConnectionHandlers();

      // Connect with retry logic
      await this.connectWithRetry();

      // Create optimized indexes
      await this.createOptimizedIndexes();

      // Set up query monitoring
      this.setupQueryMonitoring();

      // Start health monitoring
      this.startHealthMonitoring();

      logger.info('Comprehensive database service initialized successfully');
      return true;

    } catch (error) {
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'database',
        operation: 'initialize'
      });
      logger.error('Failed to initialize database service:', apiError);
      throw apiError;
    }
  }

  /**
   * Connect to MongoDB with retry logic and exponential backoff
   */
  async connectWithRetry() {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await mongoose.connect(this.config.uri, {
          ...this.config.options,
          // Additional optimization options
          bufferCommands: false,
          bufferMaxEntries: 0,
          readPreference: 'primaryPreferred',
          writeConcern: { w: 'majority', j: true },
          readConcern: { level: 'majority' }
        });

        this.isConnected = true;
        this.connectionRetries = 0;
        logger.info(`Database connected successfully on attempt ${attempt}`);
        return;

      } catch (error) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000); // Exponential backoff, max 30s
        
        logger.warn(`Database connection attempt ${attempt}/${this.maxRetries} failed:`, {
          error: error.message,
          nextRetryIn: delay
        });

        if (attempt === this.maxRetries) {
          throw new Error(`Failed to connect to database after ${this.maxRetries} attempts: ${error.message}`);
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Set up connection event handlers
   */
  setupConnectionHandlers() {
    mongoose.connection.on('connected', () => {
      this.isConnected = true;
      logger.info('Database connection established');
      this.updateConnectionPoolStats();
    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
      logger.warn('Database connection lost');
    });

    mongoose.connection.on('error', (error) => {
      this.isConnected = false;
      logger.error('Database connection error:', error);
      
      // Attempt to reconnect
      this.handleConnectionError(error);
    });

    mongoose.connection.on('reconnected', () => {
      this.isConnected = true;
      logger.info('Database reconnected successfully');
    });

    // Monitor connection pool events
    mongoose.connection.on('fullsetup', () => {
      logger.info('Database replica set connection established');
    });
  }

  /**
   * Handle connection errors with automatic recovery
   */
  async handleConnectionError(error) {
    if (this.connectionRetries < this.maxRetries) {
      this.connectionRetries++;
      const delay = Math.min(1000 * Math.pow(2, this.connectionRetries), 30000);
      
      logger.info(`Attempting database reconnection in ${delay}ms (attempt ${this.connectionRetries}/${this.maxRetries})`);
      
      setTimeout(async () => {
        try {
          await this.connectWithRetry();
        } catch (reconnectError) {
          logger.error('Database reconnection failed:', reconnectError);
        }
      }, delay);
    } else {
      logger.error('Maximum database reconnection attempts reached');
      
      // Create critical error
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'database',
        operation: 'connection',
        category: 'database'
      });
      
      // Could trigger alerts or failover mechanisms here
      throw apiError;
    }
  }

  /**
   * Create optimized indexes for all collections
   */
  async createOptimizedIndexes() {
    logger.info('Creating optimized database indexes...');
    
    const db = mongoose.connection.db;
    const indexCreationPromises = [];

    for (const [collectionName, indexes] of Object.entries(OPTIMIZED_INDEXES)) {
      for (const indexSpec of indexes) {
        const promise = this.createIndexSafely(db, collectionName, indexSpec);
        indexCreationPromises.push(promise);
      }
    }

    try {
      const results = await Promise.allSettled(indexCreationPromises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      logger.info(`Index creation completed: ${successful} successful, ${failed} failed`);

      // Log failed index creations
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          logger.warn('Failed to create index:', {
            error: result.reason.message,
            index: index
          });
        }
      });

    } catch (error) {
      logger.error('Error during index creation:', error);
    }
  }

  /**
   * Safely create an index with error handling
   */
  async createIndexSafely(db, collectionName, indexSpec) {
    try {
      const collection = db.collection(collectionName);
      
      // Check if index already exists
      const existingIndexes = await collection.indexes();
      const indexName = this.generateIndexName(indexSpec);
      
      const indexExists = existingIndexes.some(idx => 
        idx.name === indexName || this.compareIndexSpecs(idx.key, indexSpec)
      );

      if (!indexExists) {
        await collection.createIndex(indexSpec, {
          background: true, // Non-blocking index creation
          name: indexName
        });
        
        logger.debug(`Created index on ${collectionName}:`, indexSpec);
      } else {
        logger.debug(`Index already exists on ${collectionName}:`, indexSpec);
      }

    } catch (error) {
      // Don't throw for index creation errors, just log them
      logger.warn(`Failed to create index on ${collectionName}:`, {
        indexSpec,
        error: error.message
      });
    }
  }

  /**
   * Generate a consistent index name
   */
  generateIndexName(indexSpec) {
    const keys = Object.keys(indexSpec);
    const directions = Object.values(indexSpec);
    return keys.map((key, i) => `${key}_${directions[i]}`).join('_');
  }

  /**
   * Compare index specifications
   */
  compareIndexSpecs(existing, target) {
    const existingKeys = Object.keys(existing);
    const targetKeys = Object.keys(target);
    
    if (existingKeys.length !== targetKeys.length) return false;
    
    return existingKeys.every(key => 
      targetKeys.includes(key) && existing[key] === target[key]
    );
  }

  /**
   * Set up query performance monitoring
   */
  setupQueryMonitoring() {
    if (!this.config.performance.enableProfiling) return;

    // Enable MongoDB profiling for slow operations
    this.enableMongoDBProfiling();

    // Set up Mongoose query middleware for monitoring
    mongoose.plugin((schema) => {
      schema.pre(/^find/, function() {
        this._startTime = Date.now();
      });

      schema.post(/^find/, function() {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          this.constructor.db.emit('queryExecuted', {
            operation: this.op,
            duration,
            collection: this.model.collection.name,
            filter: this.getFilter()
          });
        }
      });
    });

    // Listen for query execution events
    mongoose.connection.on('queryExecuted', (queryInfo) => {
      this.trackQueryPerformance(queryInfo);
    });
  }

  /**
   * Enable MongoDB profiling for slow operations
   */
  async enableMongoDBProfiling() {
    try {
      const db = mongoose.connection.db;
      await db.admin().command({
        profile: 2, // Profile all operations
        slowms: this.config.performance.slowQueryThreshold,
        sampleRate: 1.0
      });

      logger.info(`MongoDB profiling enabled for queries > ${this.config.performance.slowQueryThreshold}ms`);
    } catch (error) {
      logger.warn('Failed to enable MongoDB profiling:', error.message);
    }
  }

  /**
   * Track query performance metrics
   */
  trackQueryPerformance(queryInfo) {
    this.healthMetrics.totalQueries++;

    // Track slow queries
    if (queryInfo.duration > this.config.performance.slowQueryThreshold) {
      this.healthMetrics.slowQueries++;
      
      this.slowQueries.unshift({
        ...queryInfo,
        timestamp: new Date().toISOString()
      });

      // Keep only recent slow queries
      if (this.slowQueries.length > 100) {
        this.slowQueries = this.slowQueries.slice(0, 100);
      }

      logger.warn('Slow query detected:', {
        duration: queryInfo.duration,
        operation: queryInfo.operation,
        collection: queryInfo.collection
      });
    }

    // Update average response time
    this.updateAverageResponseTime(queryInfo.duration);

    // Track query statistics by collection
    const collectionStats = this.queryStats.get(queryInfo.collection) || {
      count: 0,
      totalDuration: 0,
      slowQueries: 0
    };

    collectionStats.count++;
    collectionStats.totalDuration += queryInfo.duration;
    
    if (queryInfo.duration > this.config.performance.slowQueryThreshold) {
      collectionStats.slowQueries++;
    }

    this.queryStats.set(queryInfo.collection, collectionStats);
  }

  /**
   * Update average response time using exponential moving average
   */
  updateAverageResponseTime(duration) {
    const alpha = 0.1; // Smoothing factor
    this.healthMetrics.averageResponseTime = 
      (alpha * duration) + ((1 - alpha) * this.healthMetrics.averageResponseTime);
  }

  /**
   * Update connection pool statistics
   */
  updateConnectionPoolStats() {
    if (mongoose.connection.readyState === 1) {
      const db = mongoose.connection.db;
      
      // Get connection pool stats if available
      if (db.serverConfig && db.serverConfig.s && db.serverConfig.s.pool) {
        const pool = db.serverConfig.s.pool;
        this.healthMetrics.connectionPoolStats = {
          totalConnections: pool.totalConnectionCount || 0,
          availableConnections: pool.availableConnectionCount || 0,
          checkedOutConnections: pool.checkedOutConnectionCount || 0,
          minPoolSize: this.config.options.minPoolSize,
          maxPoolSize: this.config.options.maxPoolSize
        };
      }
    }
  }

  /**
   * Start health monitoring interval
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.updateConnectionPoolStats();
      this.logHealthMetrics();
    }, 60000); // Every minute
  }

  /**
   * Log health metrics
   */
  logHealthMetrics() {
    const metrics = {
      ...this.healthMetrics,
      isConnected: this.isConnected,
      queryStatsSnapshot: Object.fromEntries(this.queryStats),
      recentSlowQueries: this.slowQueries.slice(0, 5)
    };

    logger.info('Database health metrics:', metrics);
  }

  /**
   * Get comprehensive health status
   */
  getHealthStatus() {
    return {
      isConnected: this.isConnected,
      connectionRetries: this.connectionRetries,
      metrics: this.healthMetrics,
      queryStats: Object.fromEntries(this.queryStats),
      recentSlowQueries: this.slowQueries.slice(0, 10),
      config: {
        uri: this.config.uri.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
        poolSize: {
          min: this.config.options.minPoolSize,
          max: this.config.options.maxPoolSize
        },
        timeouts: {
          serverSelection: this.config.options.serverSelectionTimeoutMS,
          connection: this.config.options.connectTimeoutMS
        }
      }
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down database service...');
    
    try {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('Database connection closed gracefully');
    } catch (error) {
      logger.error('Error during database shutdown:', error);
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const comprehensiveDatabaseService = new ComprehensiveDatabaseService();

export default comprehensiveDatabaseService;
export { ComprehensiveDatabaseService, OPTIMIZED_INDEXES };
