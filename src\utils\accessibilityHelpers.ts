/**
 * Accessibility Helpers
 * 
 * Comprehensive accessibility utilities for error handling components.
 * Ensures WCAG 2.1 compliance and provides excellent screen reader
 * and keyboard navigation support.
 * 
 * Features:
 * - Screen reader announcements
 * - Keyboard navigation management
 * - Focus management
 * - ARIA attributes helpers
 * - Color contrast validation
 * - Text alternatives
 * 
 * @version 1.0.0
 */

// ============================================================================
// INTERFACES
// ============================================================================

export interface AnnouncementOptions {
  priority: 'polite' | 'assertive';
  delay?: number;
  clear?: boolean;
}

export interface FocusOptions {
  preventScroll?: boolean;
  selectText?: boolean;
  restoreFocus?: boolean;
}

export interface KeyboardNavigationOptions {
  trapFocus?: boolean;
  escapeToClose?: boolean;
  arrowNavigation?: boolean;
  homeEndNavigation?: boolean;
}

// ============================================================================
// SCREEN READER ANNOUNCEMENTS
// ============================================================================

class ScreenReaderAnnouncer {
  private static instance: ScreenReaderAnnouncer;
  private politeRegion: HTMLElement | null = null;
  private assertiveRegion: HTMLElement | null = null;

  private constructor() {
    this.createLiveRegions();
  }

  public static getInstance(): ScreenReaderAnnouncer {
    if (!ScreenReaderAnnouncer.instance) {
      ScreenReaderAnnouncer.instance = new ScreenReaderAnnouncer();
    }
    return ScreenReaderAnnouncer.instance;
  }

  private createLiveRegions(): void {
    // Create polite live region
    this.politeRegion = document.createElement('div');
    this.politeRegion.setAttribute('aria-live', 'polite');
    this.politeRegion.setAttribute('aria-atomic', 'true');
    this.politeRegion.className = 'sr-only';
    this.politeRegion.id = 'polite-announcements';

    // Create assertive live region
    this.assertiveRegion = document.createElement('div');
    this.assertiveRegion.setAttribute('aria-live', 'assertive');
    this.assertiveRegion.setAttribute('aria-atomic', 'true');
    this.assertiveRegion.className = 'sr-only';
    this.assertiveRegion.id = 'assertive-announcements';

    // Add to document
    document.body.appendChild(this.politeRegion);
    document.body.appendChild(this.assertiveRegion);
  }

  public announce(message: string, options: AnnouncementOptions = { priority: 'polite' }): void {
    const region = options.priority === 'assertive' ? this.assertiveRegion : this.politeRegion;
    
    if (!region) return;

    // Clear previous announcement if requested
    if (options.clear) {
      region.textContent = '';
    }

    // Add delay if specified
    const announceMessage = () => {
      region.textContent = message;
    };

    if (options.delay) {
      setTimeout(announceMessage, options.delay);
    } else {
      announceMessage();
    }
  }

  public announceError(error: string, recovery?: string): void {
    let message = `Error: ${error}`;
    if (recovery) {
      message += ` ${recovery}`;
    }
    
    this.announce(message, { priority: 'assertive' });
  }

  public announceSuccess(message: string): void {
    this.announce(`Success: ${message}`, { priority: 'polite' });
  }

  public announceProgress(current: number, total: number, description?: string): void {
    const percentage = Math.round((current / total) * 100);
    let message = `Progress: ${percentage}% complete`;
    
    if (description) {
      message += `, ${description}`;
    }
    
    this.announce(message, { priority: 'polite' });
  }
}

// ============================================================================
// FOCUS MANAGEMENT
// ============================================================================

class FocusManager {
  private static instance: FocusManager;
  private focusHistory: HTMLElement[] = [];
  private trapStack: HTMLElement[] = [];

  private constructor() {}

  public static getInstance(): FocusManager {
    if (!FocusManager.instance) {
      FocusManager.instance = new FocusManager();
    }
    return FocusManager.instance;
  }

  public saveFocus(): void {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && activeElement !== document.body) {
      this.focusHistory.push(activeElement);
    }
  }

  public restoreFocus(): boolean {
    const lastFocused = this.focusHistory.pop();
    if (lastFocused && document.contains(lastFocused)) {
      lastFocused.focus();
      return true;
    }
    return false;
  }

  public focusElement(element: HTMLElement, options: FocusOptions = {}): void {
    if (!options.restoreFocus) {
      this.saveFocus();
    }

    element.focus({ preventScroll: options.preventScroll });

    if (options.selectText && element instanceof HTMLInputElement) {
      element.select();
    }
  }

  public trapFocus(container: HTMLElement): () => void {
    this.trapStack.push(container);
    
    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);

    // Focus first element
    firstElement?.focus();

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
      this.trapStack.pop();
    };
  }

  public getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
  }

  public getNextFocusableElement(current: HTMLElement, direction: 'next' | 'previous' = 'next'): HTMLElement | null {
    const focusableElements = this.getFocusableElements(document.body);
    const currentIndex = focusableElements.indexOf(current);
    
    if (currentIndex === -1) return null;

    const nextIndex = direction === 'next' 
      ? (currentIndex + 1) % focusableElements.length
      : (currentIndex - 1 + focusableElements.length) % focusableElements.length;

    return focusableElements[nextIndex];
  }
}

// ============================================================================
// KEYBOARD NAVIGATION
// ============================================================================

export class KeyboardNavigationManager {
  private container: HTMLElement;
  private options: KeyboardNavigationOptions;
  private cleanupFunctions: (() => void)[] = [];

  constructor(container: HTMLElement, options: KeyboardNavigationOptions = {}) {
    this.container = container;
    this.options = {
      trapFocus: false,
      escapeToClose: true,
      arrowNavigation: false,
      homeEndNavigation: false,
      ...options
    };

    this.initialize();
  }

  private initialize(): void {
    // Set up focus trap if requested
    if (this.options.trapFocus) {
      const cleanup = focusManager.trapFocus(this.container);
      this.cleanupFunctions.push(cleanup);
    }

    // Set up keyboard event handlers
    const handleKeyDown = (event: KeyboardEvent) => {
      this.handleKeyDown(event);
    };

    this.container.addEventListener('keydown', handleKeyDown);
    this.cleanupFunctions.push(() => {
      this.container.removeEventListener('keydown', handleKeyDown);
    });
  }

  private handleKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Escape':
        if (this.options.escapeToClose) {
          this.handleEscape(event);
        }
        break;
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        if (this.options.arrowNavigation) {
          this.handleArrowNavigation(event);
        }
        break;
      case 'Home':
      case 'End':
        if (this.options.homeEndNavigation) {
          this.handleHomeEnd(event);
        }
        break;
    }
  }

  private handleEscape(event: KeyboardEvent): void {
    event.preventDefault();
    
    // Dispatch custom escape event
    const escapeEvent = new CustomEvent('keyboardEscape', {
      bubbles: true,
      cancelable: true
    });
    
    this.container.dispatchEvent(escapeEvent);
  }

  private handleArrowNavigation(event: KeyboardEvent): void {
    const focusableElements = focusManager.getFocusableElements(this.container);
    const currentElement = document.activeElement as HTMLElement;
    const currentIndex = focusableElements.indexOf(currentElement);

    if (currentIndex === -1) return;

    let nextIndex: number;

    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowLeft':
        nextIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length;
        break;
      case 'ArrowDown':
      case 'ArrowRight':
        nextIndex = (currentIndex + 1) % focusableElements.length;
        break;
      default:
        return;
    }

    event.preventDefault();
    focusableElements[nextIndex]?.focus();
  }

  private handleHomeEnd(event: KeyboardEvent): void {
    const focusableElements = focusManager.getFocusableElements(this.container);
    
    if (focusableElements.length === 0) return;

    event.preventDefault();

    if (event.key === 'Home') {
      focusableElements[0]?.focus();
    } else if (event.key === 'End') {
      focusableElements[focusableElements.length - 1]?.focus();
    }
  }

  public destroy(): void {
    this.cleanupFunctions.forEach(cleanup => cleanup());
    this.cleanupFunctions = [];
  }
}

// ============================================================================
// ARIA HELPERS
// ============================================================================

export function setAriaAttributes(element: HTMLElement, attributes: Record<string, string | boolean | null>): void {
  Object.entries(attributes).forEach(([key, value]) => {
    const ariaKey = key.startsWith('aria-') ? key : `aria-${key}`;
    
    if (value === null) {
      element.removeAttribute(ariaKey);
    } else {
      element.setAttribute(ariaKey, String(value));
    }
  });
}

export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
  screenReaderAnnouncer.announce(message, { priority });
}

export function announceError(error: string, recovery?: string): void {
  screenReaderAnnouncer.announceError(error, recovery);
}

export function announceSuccess(message: string): void {
  screenReaderAnnouncer.announceSuccess(message);
}

export function announceProgress(current: number, total: number, description?: string): void {
  screenReaderAnnouncer.announceProgress(current, total, description);
}

// ============================================================================
// COLOR CONTRAST HELPERS
// ============================================================================

export function getContrastRatio(color1: string, color2: string): number {
  const luminance1 = getLuminance(color1);
  const luminance2 = getLuminance(color2);
  
  const lighter = Math.max(luminance1, luminance2);
  const darker = Math.min(luminance1, luminance2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

export function meetsWCAGContrast(color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean {
  const ratio = getContrastRatio(color1, color2);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
}

function getLuminance(color: string): number {
  // Convert color to RGB values
  const rgb = hexToRgb(color);
  if (!rgb) return 0;

  // Convert to relative luminance
  const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// ============================================================================
// SINGLETON INSTANCES
// ============================================================================

export const screenReaderAnnouncer = ScreenReaderAnnouncer.getInstance();
export const focusManager = FocusManager.getInstance();

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createAccessibleButton(
  text: string,
  onClick: () => void,
  options: {
    ariaLabel?: string;
    ariaDescribedBy?: string;
    disabled?: boolean;
    className?: string;
  } = {}
): HTMLButtonElement {
  const button = document.createElement('button');
  button.textContent = text;
  button.onclick = onClick;
  
  if (options.ariaLabel) {
    button.setAttribute('aria-label', options.ariaLabel);
  }
  
  if (options.ariaDescribedBy) {
    button.setAttribute('aria-describedby', options.ariaDescribedBy);
  }
  
  if (options.disabled) {
    button.disabled = true;
  }
  
  if (options.className) {
    button.className = options.className;
  }
  
  return button;
}

export function createAccessibleAlert(message: string, type: 'error' | 'warning' | 'info' | 'success' = 'info'): HTMLDivElement {
  const alert = document.createElement('div');
  alert.setAttribute('role', 'alert');
  alert.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');
  alert.textContent = message;
  
  const typeClasses = {
    error: 'bg-red-50 text-red-800 border-red-200',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
    info: 'bg-blue-50 text-blue-800 border-blue-200',
    success: 'bg-green-50 text-green-800 border-green-200'
  };
  
  alert.className = `p-4 rounded border ${typeClasses[type]}`;
  
  return alert;
}

export function addSkipLink(targetId: string, text: string = 'Skip to main content'): void {
  const skipLink = document.createElement('a');
  skipLink.href = `#${targetId}`;
  skipLink.textContent = text;
  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50';
  
  document.body.insertBefore(skipLink, document.body.firstChild);
}

// ============================================================================
// CSS CLASSES FOR SCREEN READERS
// ============================================================================

// Add these CSS classes to your global styles:
/*
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
*/
