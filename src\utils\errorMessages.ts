/**
 * User-Friendly Error Messages Dictionary
 * 
 * Comprehensive error message system that provides contextual,
 * actionable guidance for users. Transforms technical errors
 * into clear, helpful communication.
 * 
 * Features:
 * - Context-aware error messages
 * - Actionable recovery instructions
 * - Multi-language support ready
 * - Severity-based messaging
 * - User journey consideration
 * - Accessibility-friendly content
 * 
 * @version 1.0.0
 */

import { ErrorType, ErrorCategory } from '../context/ErrorContext';

// ============================================================================
// INTERFACES
// ============================================================================

export interface ErrorMessage {
  title: string;
  description: string;
  instructions: string[];
  actions: ErrorAction[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  userImpact: string;
  technicalNote?: string;
}

export interface ErrorAction {
  label: string;
  type: 'primary' | 'secondary' | 'link';
  action: string;
  icon?: string;
  shortcut?: string;
}

export interface ErrorContext {
  feature?: string;
  action?: string;
  userJourney?: string;
  dataType?: string;
  timeOfDay?: 'morning' | 'afternoon' | 'evening' | 'night';
  userExperience?: 'new' | 'intermediate' | 'expert';
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}

// ============================================================================
// ERROR MESSAGE TEMPLATES
// ============================================================================

const ERROR_TEMPLATES: Record<string, ErrorMessage> = {
  // Network Errors
  'network_connection_lost': {
    title: 'Connection Lost',
    description: 'Your internet connection was interrupted while loading data.',
    instructions: [
      'Check your internet connection',
      'Try refreshing the page',
      'If using WiFi, try moving closer to your router',
      'Consider switching to mobile data temporarily'
    ],
    actions: [
      { label: 'Try Again', type: 'primary', action: 'retry', icon: 'refresh', shortcut: 'Ctrl+R' },
      { label: 'Check Connection', type: 'secondary', action: 'check_network', icon: 'wifi' },
      { label: 'Work Offline', type: 'link', action: 'offline_mode', icon: 'offline' }
    ],
    severity: 'medium',
    userImpact: 'You cannot access real-time data until your connection is restored.',
    technicalNote: 'Network request failed or timed out'
  },

  'network_slow_connection': {
    title: 'Slow Connection Detected',
    description: 'Your internet connection is slower than usual, which may affect performance.',
    instructions: [
      'Data loading may take longer than normal',
      'Consider reducing the number of charts or indicators',
      'Try closing other applications using internet',
      'Switch to a faster connection if available'
    ],
    actions: [
      { label: 'Continue Anyway', type: 'primary', action: 'continue', icon: 'check' },
      { label: 'Optimize Settings', type: 'secondary', action: 'optimize', icon: 'settings' },
      { label: 'Learn More', type: 'link', action: 'help_performance', icon: 'info' }
    ],
    severity: 'low',
    userImpact: 'Features will work but may be slower than usual.',
    technicalNote: 'Connection speed below optimal threshold'
  },

  // API Errors
  'api_server_error': {
    title: 'Server Temporarily Unavailable',
    description: 'Our servers are experiencing high traffic or temporary issues.',
    instructions: [
      'This is usually resolved within a few minutes',
      'Your data and settings are safe',
      'Try again in a moment',
      'Check our status page for updates'
    ],
    actions: [
      { label: 'Try Again', type: 'primary', action: 'retry', icon: 'refresh' },
      { label: 'Check Status', type: 'secondary', action: 'status_page', icon: 'external' },
      { label: 'Contact Support', type: 'link', action: 'support', icon: 'help' }
    ],
    severity: 'high',
    userImpact: 'Some features may be temporarily unavailable.',
    technicalNote: '5xx server error response'
  },

  'api_rate_limit': {
    title: 'Too Many Requests',
    description: 'You\'ve made too many requests in a short time. Please wait a moment.',
    instructions: [
      'Wait 60 seconds before trying again',
      'Avoid rapidly clicking buttons or refreshing',
      'Consider upgrading for higher limits',
      'Use filters to reduce data requests'
    ],
    actions: [
      { label: 'Wait and Retry', type: 'primary', action: 'wait_retry', icon: 'clock' },
      { label: 'Upgrade Plan', type: 'secondary', action: 'upgrade', icon: 'star' },
      { label: 'Learn About Limits', type: 'link', action: 'help_limits', icon: 'info' }
    ],
    severity: 'medium',
    userImpact: 'Requests are temporarily limited to prevent overload.',
    technicalNote: '429 rate limit exceeded'
  },

  // Authentication Errors
  'auth_session_expired': {
    title: 'Session Expired',
    description: 'Your login session has expired for security reasons.',
    instructions: [
      'Click "Log In Again" to continue',
      'Your work will be saved automatically',
      'Consider enabling "Remember Me" for longer sessions',
      'Check if your system clock is correct'
    ],
    actions: [
      { label: 'Log In Again', type: 'primary', action: 'login', icon: 'login' },
      { label: 'Save Work First', type: 'secondary', action: 'save', icon: 'save' },
      { label: 'Why Did This Happen?', type: 'link', action: 'help_session', icon: 'info' }
    ],
    severity: 'high',
    userImpact: 'You need to log in again to continue using the application.',
    technicalNote: '401 authentication required'
  },

  'auth_insufficient_permissions': {
    title: 'Access Not Allowed',
    description: 'You don\'t have permission to access this feature.',
    instructions: [
      'Contact your administrator for access',
      'Check if you need to upgrade your plan',
      'Verify you\'re logged into the correct account',
      'Some features require additional verification'
    ],
    actions: [
      { label: 'Contact Admin', type: 'primary', action: 'contact_admin', icon: 'user' },
      { label: 'Upgrade Plan', type: 'secondary', action: 'upgrade', icon: 'star' },
      { label: 'View Permissions', type: 'link', action: 'view_permissions', icon: 'shield' }
    ],
    severity: 'medium',
    userImpact: 'This feature is not available with your current permissions.',
    technicalNote: '403 forbidden access'
  },

  // Data Errors
  'data_not_found': {
    title: 'Data Not Available',
    description: 'The information you\'re looking for couldn\'t be found.',
    instructions: [
      'Check if the symbol or date range is correct',
      'Try a different time period',
      'Verify the market is open for this instrument',
      'Some data may have limited history'
    ],
    actions: [
      { label: 'Try Different Symbol', type: 'primary', action: 'change_symbol', icon: 'search' },
      { label: 'Adjust Date Range', type: 'secondary', action: 'change_dates', icon: 'calendar' },
      { label: 'Browse Available Data', type: 'link', action: 'browse_data', icon: 'list' }
    ],
    severity: 'low',
    userImpact: 'This specific data is not available, but you can try alternatives.',
    technicalNote: '404 resource not found'
  },

  'data_validation_failed': {
    title: 'Invalid Information',
    description: 'Some of the information you entered doesn\'t look right.',
    instructions: [
      'Check the highlighted fields for errors',
      'Make sure dates are in the correct format',
      'Verify numbers are within valid ranges',
      'Remove any special characters if not allowed'
    ],
    actions: [
      { label: 'Fix Errors', type: 'primary', action: 'fix_validation', icon: 'edit' },
      { label: 'Reset Form', type: 'secondary', action: 'reset_form', icon: 'refresh' },
      { label: 'See Examples', type: 'link', action: 'show_examples', icon: 'info' }
    ],
    severity: 'low',
    userImpact: 'Please correct the errors to continue.',
    technicalNote: 'Form validation failed'
  },

  // System Errors
  'system_maintenance': {
    title: 'Scheduled Maintenance',
    description: 'We\'re performing scheduled maintenance to improve our service.',
    instructions: [
      'Maintenance typically takes 15-30 minutes',
      'Your data and settings are safe',
      'Check back shortly',
      'Follow us on social media for updates'
    ],
    actions: [
      { label: 'Check Back Later', type: 'primary', action: 'close', icon: 'clock' },
      { label: 'Get Updates', type: 'secondary', action: 'follow_updates', icon: 'bell' },
      { label: 'Emergency Contact', type: 'link', action: 'emergency_contact', icon: 'phone' }
    ],
    severity: 'high',
    userImpact: 'The service is temporarily unavailable during maintenance.',
    technicalNote: 'Planned maintenance window'
  },

  'system_overload': {
    title: 'High Traffic',
    description: 'We\'re experiencing higher than normal traffic right now.',
    instructions: [
      'Please be patient as we handle the increased load',
      'Try again in a few minutes',
      'Consider using the service during off-peak hours',
      'Essential features should still work'
    ],
    actions: [
      { label: 'Wait and Retry', type: 'primary', action: 'wait_retry', icon: 'clock' },
      { label: 'Use Basic Mode', type: 'secondary', action: 'basic_mode', icon: 'minimize' },
      { label: 'Peak Hours Info', type: 'link', action: 'peak_hours', icon: 'info' }
    ],
    severity: 'medium',
    userImpact: 'Some features may be slower or temporarily unavailable.',
    technicalNote: 'Server capacity exceeded'
  }
};

// ============================================================================
// CONTEXTUAL MESSAGE GENERATOR
// ============================================================================

export class ErrorMessageGenerator {
  private static instance: ErrorMessageGenerator;
  private userContext: Partial<ErrorContext> = {};

  private constructor() {}

  public static getInstance(): ErrorMessageGenerator {
    if (!ErrorMessageGenerator.instance) {
      ErrorMessageGenerator.instance = new ErrorMessageGenerator();
    }
    return ErrorMessageGenerator.instance;
  }

  public setUserContext(context: Partial<ErrorContext>): void {
    this.userContext = { ...this.userContext, ...context };
  }

  public generateMessage(
    error: Error,
    errorType: ErrorType,
    errorCategory: ErrorCategory,
    context?: Partial<ErrorContext>
  ): ErrorMessage {
    const fullContext = { ...this.userContext, ...context };
    const messageKey = this.determineMessageKey(error, errorType, fullContext);
    const baseMessage = ERROR_TEMPLATES[messageKey] || this.getDefaultMessage(errorType);

    return this.personalizeMessage(baseMessage, fullContext, error);
  }

  private determineMessageKey(
    error: Error,
    errorType: ErrorType,
    context: Partial<ErrorContext>
  ): string {
    const message = error.message.toLowerCase();
    const status = (error as any).status;

    // Network errors
    if (errorType === 'network') {
      if (message.includes('slow') || message.includes('timeout')) {
        return 'network_slow_connection';
      }
      return 'network_connection_lost';
    }

    // API errors
    if (errorType === 'api') {
      if (status === 429) return 'api_rate_limit';
      if (status >= 500) return 'api_server_error';
      if (status === 404) return 'data_not_found';
    }

    // Authentication errors
    if (errorType === 'authentication') {
      if (status === 401) return 'auth_session_expired';
      if (status === 403) return 'auth_insufficient_permissions';
    }

    // Validation errors
    if (errorType === 'validation') {
      return 'data_validation_failed';
    }

    // System errors
    if (errorType === 'system') {
      if (message.includes('maintenance')) return 'system_maintenance';
      if (message.includes('overload') || message.includes('capacity')) {
        return 'system_overload';
      }
    }

    // Default fallback
    return this.getDefaultMessageKey(errorType);
  }

  private personalizeMessage(
    baseMessage: ErrorMessage,
    context: Partial<ErrorContext>,
    error: Error
  ): ErrorMessage {
    let personalizedMessage = { ...baseMessage };

    // Adjust for user experience level
    if (context.userExperience === 'new') {
      personalizedMessage = this.simplifyForNewUsers(personalizedMessage);
    } else if (context.userExperience === 'expert') {
      personalizedMessage = this.addTechnicalDetails(personalizedMessage, error);
    }

    // Adjust for device type
    if (context.deviceType === 'mobile') {
      personalizedMessage = this.optimizeForMobile(personalizedMessage);
    }

    // Adjust for feature context
    if (context.feature) {
      personalizedMessage = this.addFeatureContext(personalizedMessage, context.feature);
    }

    // Adjust for time of day
    if (context.timeOfDay) {
      personalizedMessage = this.addTimeContext(personalizedMessage, context.timeOfDay);
    }

    return personalizedMessage;
  }

  private simplifyForNewUsers(message: ErrorMessage): ErrorMessage {
    return {
      ...message,
      description: this.simplifyLanguage(message.description),
      instructions: message.instructions.slice(0, 2), // Show fewer instructions
      actions: message.actions.filter(action => action.type !== 'link') // Remove advanced actions
    };
  }

  private addTechnicalDetails(message: ErrorMessage, error: Error): ErrorMessage {
    return {
      ...message,
      technicalNote: message.technicalNote || error.message,
      actions: [
        ...message.actions,
        { label: 'View Technical Details', type: 'link', action: 'technical_details', icon: 'code' }
      ]
    };
  }

  private optimizeForMobile(message: ErrorMessage): ErrorMessage {
    return {
      ...message,
      instructions: message.instructions.slice(0, 3), // Fewer instructions for mobile
      actions: message.actions.slice(0, 2) // Fewer actions for mobile
    };
  }

  private addFeatureContext(message: ErrorMessage, feature: string): ErrorMessage {
    const featureSpecificInstructions: Record<string, string[]> = {
      'signals': [
        'Try selecting a different symbol or timeframe',
        'Check if the market is currently open',
        'Consider using cached data while we resolve this'
      ],
      'charts': [
        'Try reducing the number of indicators',
        'Switch to a simpler chart type',
        'Clear your browser cache and try again'
      ],
      'portfolio': [
        'Your portfolio data is safely stored',
        'Try refreshing to see the latest values',
        'Contact support if positions look incorrect'
      ]
    };

    const additionalInstructions = featureSpecificInstructions[feature];
    if (additionalInstructions) {
      return {
        ...message,
        instructions: [...additionalInstructions, ...message.instructions]
      };
    }

    return message;
  }

  private addTimeContext(message: ErrorMessage, timeOfDay: string): ErrorMessage {
    const timeSpecificNotes: Record<string, string> = {
      'morning': 'Markets are opening - higher traffic is normal.',
      'afternoon': 'Peak trading hours - some delays may occur.',
      'evening': 'After-hours trading - limited data availability.',
      'night': 'Maintenance window - some features may be limited.'
    };

    const timeNote = timeSpecificNotes[timeOfDay];
    if (timeNote) {
      return {
        ...message,
        description: `${message.description} ${timeNote}`
      };
    }

    return message;
  }

  private simplifyLanguage(text: string): string {
    return text
      .replace(/temporarily/g, 'for now')
      .replace(/experiencing/g, 'having')
      .replace(/unavailable/g, 'not working')
      .replace(/interrupted/g, 'stopped');
  }

  private getDefaultMessage(errorType: ErrorType): ErrorMessage {
    return {
      title: 'Something Went Wrong',
      description: 'We encountered an unexpected issue. Don\'t worry, we\'re working on it.',
      instructions: [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem continues'
      ],
      actions: [
        { label: 'Try Again', type: 'primary', action: 'retry', icon: 'refresh' },
        { label: 'Contact Support', type: 'secondary', action: 'support', icon: 'help' }
      ],
      severity: 'medium',
      userImpact: 'Some features may not work as expected.'
    };
  }

  private getDefaultMessageKey(errorType: ErrorType): string {
    const defaultKeys: Record<ErrorType, string> = {
      network: 'network_connection_lost',
      api: 'api_server_error',
      authentication: 'auth_session_expired',
      authorization: 'auth_insufficient_permissions',
      validation: 'data_validation_failed',
      component: 'system_overload',
      data: 'data_not_found',
      system: 'system_overload',
      unknown: 'system_overload'
    };

    return defaultKeys[errorType] || 'system_overload';
  }
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export const errorMessageGenerator = ErrorMessageGenerator.getInstance();

export function getErrorMessage(
  error: Error,
  errorType: ErrorType,
  errorCategory: ErrorCategory,
  context?: Partial<ErrorContext>
): ErrorMessage {
  return errorMessageGenerator.generateMessage(error, errorType, errorCategory, context);
}

export function setUserContext(context: Partial<ErrorContext>): void {
  errorMessageGenerator.setUserContext(context);
}

// Initialize with default context
setUserContext({
  deviceType: window.innerWidth < 768 ? 'mobile' : window.innerWidth < 1024 ? 'tablet' : 'desktop',
  timeOfDay: (() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    if (hour < 21) return 'evening';
    return 'night';
  })()
});
