import signalsService from '../services/signalsService.js';
import { UnifiedSignalSchema, SIGNAL_TYPES, SIGNAL_SOURCES, TIMEFRAMES } from '../../schemas/unifiedSignalSchema.js';
import { APIError } from '../../../middleware/errorHandler.js';
import notificationService from '../../../services/notificationService.js';
import enhancedErrorHandlingService from '../../../services/enhancedErrorHandlingService.js';

// Use the unified signal schema for validation
const signalValidationSchema = UnifiedSignalSchema.omit({
  id: true,
  _id: true,
  createdAt: true,
  updatedAt: true,
  timestamp: true
}).extend({
  // Make some fields optional for creation
  confidence: UnifiedSignalSchema.shape.confidence.optional(),
  status: UnifiedSignalSchema.shape.status.optional(),
  timestamp: UnifiedSignalSchema.shape.timestamp.optional()
});

export const getAllSignals = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, symbol, type, source, status } = req.query;

    // Build filter object
    const filter = {};
    if (symbol) filter.symbol = symbol.toUpperCase();
    if (type) filter.type = type.toUpperCase();
    if (source) filter.source = source.toUpperCase();
    if (status) filter.status = status.toUpperCase();

    const signals = await signalsService.getAllSignals(filter, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    // Transform signals to API response format
    const transformedSignals = signals.data.map(signal =>
      signal.toApiResponse ? signal.toApiResponse() : signal
    );

    res.json({
      status: 'success',
      data: transformedSignals,
      pagination: signals.pagination
    });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: '/signals',
      method: 'GET'
    });
    next(new APIError(500, standardizedErrorHandler.getUserFriendlyMessage(standardError)));
  }
};

export const createSignal = async (req, res, next) => {
  try {
    // Validate signal data using unified schema
    const validated = signalValidationSchema.safeParse(req.body);
    if (!validated.success) {
      const standardError = await standardizedErrorHandler.handleError(
        new Error('Signal validation failed'),
        {
          endpoint: '/signals',
          method: 'POST',
          validationErrors: validated.error.errors
        }
      );
      return next(new APIError(400, 'Invalid signal data', false, standardError.id, validated.error.errors));
    }

    // Add default values
    const signalData = {
      ...validated.data,
      confidence: validated.data.confidence || 50,
      timestamp: validated.data.timestamp || new Date().toISOString(),
      source: validated.data.source || SIGNAL_SOURCES.MANUAL,
      userId: req.user?.id
    };

    const signal = await signalsService.createSignal(signalData);

    // Send notification for high-confidence signals
    if (signal.confidence >= 70) {
      try {
        await notificationService.sendSignalNotification(signal);
      } catch (notificationError) {
        // Log but don't fail the request
        console.warn('Failed to send signal notification:', notificationError);
      }
    }

    // Use standardized response format
    if (res.success) {
      res.success(
        signal.toApiResponse ? signal.toApiResponse() : signal,
        'Signal created successfully'
      );
    } else {
      res.status(201).json({
        status: 'success',
        data: signal.toApiResponse ? signal.toApiResponse() : signal,
        message: 'Signal created successfully'
      });
    }
  } catch (error) {
    const apiError = enhancedErrorHandlingService.createAPIError(error, {
      service: 'signals',
      operation: 'createSignal',
      endpoint: '/signals',
      method: 'POST',
      requestId: req.requestId,
      userId: req.user?.id
    });
    next(apiError);
  }
};

export const getSignalById = async (req, res, next) => {
  try {
    const signal = await signalsService.getSignalById(req.params.id);
    if (!signal) {
      return res.status(404).json({
        status: 'error',
        message: 'Signal not found'
      });
    }

    res.json({
      status: 'success',
      data: signal.toApiResponse ? signal.toApiResponse() : signal
    });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: `/signals/${req.params.id}`,
      method: 'GET'
    });
    next(new APIError(500, standardizedErrorHandler.getUserFriendlyMessage(standardError)));
  }
};

export const updateSignal = async (req, res, next) => {
  try {
    // Validate update data
    const updateSchema = signalValidationSchema.partial();
    const validated = updateSchema.safeParse(req.body);

    if (!validated.success) {
      const standardError = await standardizedErrorHandler.handleError(
        new Error('Signal update validation failed'),
        {
          endpoint: `/signals/${req.params.id}`,
          method: 'PUT',
          validationErrors: validated.error.errors
        }
      );
      return next(new APIError(400, 'Invalid update data', false, standardError.id, validated.error.errors));
    }

    const signal = await signalsService.updateSignal(req.params.id, validated.data);
    if (!signal) {
      return res.status(404).json({
        status: 'error',
        message: 'Signal not found'
      });
    }

    res.json({
      status: 'success',
      data: signal.toApiResponse ? signal.toApiResponse() : signal
    });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: `/signals/${req.params.id}`,
      method: 'PUT'
    });
    next(new APIError(500, standardizedErrorHandler.getUserFriendlyMessage(standardError)));
  }
};

export const deleteSignal = async (req, res, next) => {
  try {
    const signal = await signalsService.deleteSignal(req.params.id);
    if (!signal) {
      return res.status(404).json({
        status: 'error',
        message: 'Signal not found'
      });
    }
    res.json({ status: 'success', message: 'Signal deleted successfully' });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: `/signals/${req.params.id}`,
      method: 'DELETE'
    });
    next(new APIError(500, standardizedErrorHandler.getUserFriendlyMessage(standardError)));
  }
};

export const getSignalsBySymbol = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    const { limit = 10, status = 'ACTIVE' } = req.query;

    const signals = await signalsService.getSignalsBySymbol(symbol.toUpperCase(), {
      limit: parseInt(limit),
      status: status.toUpperCase()
    });

    const transformedSignals = signals.map(signal =>
      signal.toApiResponse ? signal.toApiResponse() : signal
    );

    res.json({
      status: 'success',
      data: transformedSignals
    });
  } catch (error) {
    const standardError = await standardizedErrorHandler.handleError(error, {
      endpoint: `/signals/symbol/${req.params.symbol}`,
      method: 'GET'
    });
    next(new APIError(500, standardizedErrorHandler.getUserFriendlyMessage(standardError)));
  }
};

export default {
  getAllSignals,
  createSignal,
  getSignalById,
  updateSignal,
  deleteSignal,
  getSignalsBySymbol,
};
