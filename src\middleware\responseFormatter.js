/**
 * Response Formatter Middleware
 * 
 * Provides standardized response formatting across all API endpoints:
 * - Consistent response structure
 * - Request ID tracking
 * - Performance metrics
 * - Error handling integration
 * - Response compression optimization
 * 
 * @version 1.0.0
 */

import { v4 as uuidv4 } from 'uuid';
import centralizedConfig from '../config/centralizedConfig.js';
import logger from '../utils/logger.js';

// ============================================================================
// RESPONSE FORMATTER MIDDLEWARE
// ============================================================================

/**
 * Standard response format
 */
const RESPONSE_FORMAT = {
  SUCCESS: 'success',
  ERROR: 'error',
  PARTIAL: 'partial'
};

/**
 * Response formatter middleware factory
 */
export function responseFormatter() {
  return (req, res, next) => {
    // Add request ID for tracking
    req.requestId = req.headers['x-request-id'] || uuidv4();
    res.setHeader('X-Request-ID', req.requestId);

    // Track request start time for performance metrics
    req.startTime = Date.now();

    // Add success response helper
    res.success = function(data = null, message = 'Operation successful', meta = {}) {
      const responseTime = Date.now() - req.startTime;
      
      const response = {
        status: RESPONSE_FORMAT.SUCCESS,
        message,
        data,
        meta: {
          ...meta,
          requestId: req.requestId,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`
        }
      };

      // Add pagination info if present
      if (meta.pagination) {
        response.pagination = meta.pagination;
      }

      // Log successful response
      logger.info('API Success Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: this.statusCode || 200,
        responseTime,
        userId: req.user?.id,
        dataSize: data ? JSON.stringify(data).length : 0
      });

      return this.json(response);
    };

    // Add error response helper
    res.error = function(message = 'An error occurred', statusCode = 500, errorCode = null, details = null) {
      const responseTime = Date.now() - req.startTime;
      
      const response = {
        status: RESPONSE_FORMAT.ERROR,
        message,
        meta: {
          requestId: req.requestId,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`
        }
      };

      // Add error code if provided
      if (errorCode) {
        response.errorCode = errorCode;
      }

      // Add error details in development mode
      if (centralizedConfig.get('app.isDevelopment') && details) {
        response.details = details;
      }

      // Log error response
      logger.error('API Error Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode,
        message,
        errorCode,
        responseTime,
        userId: req.user?.id
      });

      return this.status(statusCode).json(response);
    };

    // Add partial success response helper (for operations with some failures)
    res.partial = function(data = null, message = 'Operation partially successful', errors = [], meta = {}) {
      const responseTime = Date.now() - req.startTime;
      
      const response = {
        status: RESPONSE_FORMAT.PARTIAL,
        message,
        data,
        errors,
        meta: {
          ...meta,
          requestId: req.requestId,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
          successCount: meta.successCount || 0,
          errorCount: errors.length
        }
      };

      // Log partial response
      logger.warn('API Partial Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: this.statusCode || 207,
        responseTime,
        successCount: meta.successCount || 0,
        errorCount: errors.length,
        userId: req.user?.id
      });

      return this.status(207).json(response); // 207 Multi-Status
    };

    // Add paginated response helper
    res.paginated = function(data = [], pagination = {}, message = 'Data retrieved successfully', meta = {}) {
      const responseTime = Date.now() - req.startTime;
      
      const response = {
        status: RESPONSE_FORMAT.SUCCESS,
        message,
        data,
        pagination: {
          page: pagination.page || 1,
          limit: pagination.limit || 10,
          total: pagination.total || data.length,
          totalPages: pagination.totalPages || Math.ceil((pagination.total || data.length) / (pagination.limit || 10)),
          hasNext: pagination.hasNext || false,
          hasPrev: pagination.hasPrev || false
        },
        meta: {
          ...meta,
          requestId: req.requestId,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`
        }
      };

      // Log paginated response
      logger.info('API Paginated Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: this.statusCode || 200,
        responseTime,
        dataCount: data.length,
        pagination: response.pagination,
        userId: req.user?.id
      });

      return this.json(response);
    };

    // Add cached response helper
    res.cached = function(data = null, message = 'Data retrieved from cache', cacheInfo = {}, meta = {}) {
      const responseTime = Date.now() - req.startTime;
      
      const response = {
        status: RESPONSE_FORMAT.SUCCESS,
        message,
        data,
        meta: {
          ...meta,
          requestId: req.requestId,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
          cached: true,
          cacheInfo: {
            source: cacheInfo.source || 'unknown',
            age: cacheInfo.age || 0,
            ttl: cacheInfo.ttl || 0,
            ...cacheInfo
          }
        }
      };

      // Add cache headers
      this.setHeader('X-Cache-Status', 'HIT');
      this.setHeader('X-Cache-Source', cacheInfo.source || 'unknown');
      
      if (cacheInfo.age) {
        this.setHeader('X-Cache-Age', cacheInfo.age.toString());
      }

      // Log cached response
      logger.debug('API Cached Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: this.statusCode || 200,
        responseTime,
        cacheSource: cacheInfo.source,
        cacheAge: cacheInfo.age,
        userId: req.user?.id
      });

      return this.json(response);
    };

    // Override the original json method to add response headers
    const originalJson = res.json;
    res.json = function(body) {
      // Add security headers
      this.setHeader('X-Content-Type-Options', 'nosniff');
      this.setHeader('X-Frame-Options', 'DENY');
      this.setHeader('X-XSS-Protection', '1; mode=block');
      
      // Add API version header
      this.setHeader('X-API-Version', '1.0.0');
      
      // Add response time header
      const responseTime = Date.now() - req.startTime;
      this.setHeader('X-Response-Time', `${responseTime}ms`);

      // Call original json method
      return originalJson.call(this, body);
    };

    next();
  };
}

/**
 * Request ID middleware (can be used separately)
 */
export function requestIdMiddleware() {
  return (req, res, next) => {
    req.requestId = req.headers['x-request-id'] || uuidv4();
    res.setHeader('X-Request-ID', req.requestId);
    next();
  };
}

/**
 * Performance tracking middleware
 */
export function performanceTrackingMiddleware() {
  return (req, res, next) => {
    req.startTime = Date.now();
    
    // Track response completion
    res.on('finish', () => {
      const responseTime = Date.now() - req.startTime;
      const slowThreshold = centralizedConfig.get('monitoring.performance.slowRequestThreshold', 1000);
      const criticalThreshold = centralizedConfig.get('monitoring.performance.criticalRequestThreshold', 5000);
      
      // Log slow requests
      if (responseTime > slowThreshold) {
        const logLevel = responseTime > criticalThreshold ? 'error' : 'warn';
        logger[logLevel]('Slow API Request', {
          requestId: req.requestId,
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          responseTime,
          userId: req.user?.id,
          userAgent: req.headers['user-agent'],
          ip: req.ip
        });
      }
    });
    
    next();
  };
}

/**
 * Error response formatter for error handling middleware
 */
export function formatErrorResponse(error, req, res) {
  const responseTime = req.startTime ? Date.now() - req.startTime : 0;
  
  const response = {
    status: RESPONSE_FORMAT.ERROR,
    message: error.message || 'An error occurred',
    meta: {
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`
    }
  };

  // Add error code if available
  if (error.errorCode) {
    response.errorCode = error.errorCode;
  }

  // Add user-friendly suggestion if available
  if (error.suggestion) {
    response.suggestion = error.suggestion;
  }

  // Add error details in development mode
  if (centralizedConfig.get('app.isDevelopment')) {
    response.details = {
      category: error.category,
      severity: error.severity,
      originalError: error.originalError,
      context: error.context
    };
  }

  // Set appropriate status code
  const statusCode = error.statusCode || 500;
  
  return res.status(statusCode).json(response);
}

/**
 * Validation helper for common response patterns
 */
export function validateResponseData(data, schema) {
  // Basic validation - can be extended with Joi or other validation libraries
  if (schema.required && (data === null || data === undefined)) {
    throw new Error('Response data is required');
  }
  
  if (schema.type && typeof data !== schema.type) {
    throw new Error(`Response data must be of type ${schema.type}`);
  }
  
  if (schema.minLength && Array.isArray(data) && data.length < schema.minLength) {
    throw new Error(`Response data must have at least ${schema.minLength} items`);
  }
  
  return true;
}

export default responseFormatter;
