/**
 * Intelligent Cache Service
 * 
 * Provides multi-tier caching with:
 * - Redis primary cache with automatic failover
 * - In-memory secondary cache
 * - Intelligent TTL management based on data type
 * - Compression for large values
 * - Cache warming and preloading
 * - Performance monitoring and optimization
 * 
 * @version 1.0.0
 */

import Redis from 'ioredis';
import NodeCache from 'node-cache';
import zlib from 'zlib';
import { promisify } from 'util';
import centralizedConfig from '../config/centralizedConfig.js';
import enhancedErrorHandlingService from './enhancedErrorHandlingService.js';
import logger from '../utils/logger.js';

// Promisify compression functions
const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

// ============================================================================
// CACHE STRATEGIES AND CONFIGURATIONS
// ============================================================================

const CACHE_STRATEGIES = {
  MARKET_DATA: {
    ttl: 30, // 30 seconds for real-time data
    compress: false,
    warmup: true,
    pattern: 'market:*'
  },
  TECHNICAL_INDICATORS: {
    ttl: 300, // 5 minutes
    compress: true,
    warmup: false,
    pattern: 'indicators:*'
  },
  AI_ANALYSIS: {
    ttl: 600, // 10 minutes
    compress: true,
    warmup: false,
    pattern: 'ai:*'
  },
  USER_SESSIONS: {
    ttl: 86400, // 24 hours
    compress: false,
    warmup: false,
    pattern: 'session:*'
  },
  NEWS_SENTIMENT: {
    ttl: 1800, // 30 minutes
    compress: true,
    warmup: false,
    pattern: 'news:*'
  },
  STATIC_DATA: {
    ttl: 3600, // 1 hour
    compress: true,
    warmup: true,
    pattern: 'static:*'
  }
};

// ============================================================================
// INTELLIGENT CACHE SERVICE
// ============================================================================

class IntelligentCacheService {
  constructor() {
    this.config = centralizedConfig.get('cache');
    this.redis = null;
    this.fallbackCache = new NodeCache(this.config.nodeCache);
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;
    this.compressionThreshold = 1024; // 1KB
    
    // Performance metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      errors: 0,
      redisOperations: 0,
      fallbackOperations: 0,
      compressionSavings: 0,
      totalRequests: 0,
      averageResponseTime: 0
    };

    // Cache warming and strategy management
    this.warmupKeys = new Set();
    this.keyStrategies = new Map();
    this.compressionStats = new Map();
  }

  /**
   * Initialize the cache service
   */
  async initialize() {
    try {
      logger.info('Initializing intelligent cache service...');

      // Initialize Redis if enabled
      if (this.config.redis.enabled) {
        await this.initializeRedis();
      } else {
        logger.info('Redis disabled, using in-memory cache only');
      }

      // Set up cache strategies
      this.setupCacheStrategies();

      // Start performance monitoring
      this.startPerformanceMonitoring();

      logger.info('Intelligent cache service initialized successfully');
      return true;

    } catch (error) {
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'cache',
        operation: 'initialize'
      });
      logger.error('Failed to initialize cache service:', apiError);
      throw apiError;
    }
  }

  /**
   * Initialize Redis connection
   */
  async initializeRedis() {
    try {
      this.redis = new Redis({
        ...this.config.redis,
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
        lazyConnect: true,
        maxRetriesPerRequest: 3,
        connectTimeout: 5000,
        commandTimeout: 3000,
        enableReadyCheck: true
      });

      // Set up event handlers
      this.redis.on('connect', () => {
        this.isConnected = true;
        this.connectionRetries = 0;
        logger.info('Redis cache connected successfully');
      });

      this.redis.on('error', (error) => {
        this.isConnected = false;
        this.metrics.errors++;
        logger.error('Redis cache error:', error);
      });

      this.redis.on('close', () => {
        this.isConnected = false;
        logger.warn('Redis cache connection closed');
      });

      // Test connection
      await this.redis.ping();
      logger.info('Redis cache service initialized');

    } catch (error) {
      logger.warn('Redis initialization failed, using fallback cache:', error.message);
      this.isConnected = false;
    }
  }

  /**
   * Set up cache strategies for different data types
   */
  setupCacheStrategies() {
    Object.entries(CACHE_STRATEGIES).forEach(([strategyName, config]) => {
      this.keyStrategies.set(config.pattern, {
        name: strategyName,
        ...config
      });
    });

    logger.info('Cache strategies configured:', Object.keys(CACHE_STRATEGIES));
  }

  /**
   * Get cache strategy for a key
   */
  getStrategyForKey(key) {
    for (const [pattern, strategy] of this.keyStrategies) {
      const regex = new RegExp(pattern.replace('*', '.*'));
      if (regex.test(key)) {
        return strategy;
      }
    }
    
    // Default strategy
    return {
      name: 'DEFAULT',
      ttl: this.config.ttl.default,
      compress: false,
      warmup: false
    };
  }

  /**
   * Set cache value with intelligent strategy
   */
  async set(key, value, customTTL = null) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const strategy = this.getStrategyForKey(key);
      const ttl = customTTL || strategy.ttl;
      
      // Serialize value
      let serializedValue = JSON.stringify(value);
      let compressed = false;

      // Apply compression if needed
      if (strategy.compress && serializedValue.length > this.compressionThreshold) {
        const originalSize = Buffer.byteLength(serializedValue, 'utf8');
        const compressedBuffer = await gzip(serializedValue);
        const compressedSize = compressedBuffer.length;
        
        if (compressedSize < originalSize * 0.8) { // Only use if 20%+ savings
          serializedValue = compressedBuffer.toString('base64');
          compressed = true;
          
          this.metrics.compressionSavings += (originalSize - compressedSize);
          this.updateCompressionStats(key, originalSize, compressedSize);
        }
      }

      // Store metadata with value
      const cacheEntry = {
        data: serializedValue,
        compressed,
        timestamp: Date.now(),
        strategy: strategy.name,
        ttl
      };

      // Try Redis first, fallback to in-memory
      let success = false;
      
      if (this.isConnected && this.redis) {
        try {
          await this.redis.setex(key, ttl, JSON.stringify(cacheEntry));
          this.metrics.redisOperations++;
          success = true;
        } catch (redisError) {
          logger.warn('Redis set failed, using fallback:', redisError.message);
          this.metrics.errors++;
        }
      }

      if (!success) {
        this.fallbackCache.set(key, cacheEntry, ttl);
        this.metrics.fallbackOperations++;
      }

      // Add to warmup keys if strategy requires it
      if (strategy.warmup) {
        this.warmupKeys.add(key);
      }

      this.updateResponseTime(Date.now() - startTime);
      return true;

    } catch (error) {
      this.metrics.errors++;
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'cache',
        operation: 'set',
        key
      });
      logger.error('Cache set operation failed:', apiError);
      throw apiError;
    }
  }

  /**
   * Get cache value with intelligent fallback
   */
  async get(key) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      let cacheEntry = null;
      let fromRedis = false;

      // Try Redis first
      if (this.isConnected && this.redis) {
        try {
          const redisValue = await this.redis.get(key);
          if (redisValue) {
            cacheEntry = JSON.parse(redisValue);
            fromRedis = true;
            this.metrics.redisOperations++;
          }
        } catch (redisError) {
          logger.warn('Redis get failed, trying fallback:', redisError.message);
          this.metrics.errors++;
        }
      }

      // Try fallback cache if Redis failed or returned null
      if (!cacheEntry) {
        cacheEntry = this.fallbackCache.get(key);
        if (cacheEntry) {
          this.metrics.fallbackOperations++;
        }
      }

      if (!cacheEntry) {
        this.metrics.misses++;
        this.updateResponseTime(Date.now() - startTime);
        return null;
      }

      // Decompress if needed
      let data = cacheEntry.data;
      if (cacheEntry.compressed) {
        try {
          const compressedBuffer = Buffer.from(data, 'base64');
          const decompressed = await gunzip(compressedBuffer);
          data = decompressed.toString('utf8');
        } catch (decompressionError) {
          logger.error('Decompression failed:', decompressionError);
          this.metrics.errors++;
          return null;
        }
      }

      // Parse the actual data
      const parsedData = JSON.parse(data);
      
      this.metrics.hits++;
      this.updateResponseTime(Date.now() - startTime);

      // Log cache hit details
      logger.debug('Cache hit:', {
        key,
        strategy: cacheEntry.strategy,
        compressed: cacheEntry.compressed,
        fromRedis,
        age: Date.now() - cacheEntry.timestamp
      });

      return parsedData;

    } catch (error) {
      this.metrics.errors++;
      this.metrics.misses++;
      
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'cache',
        operation: 'get',
        key
      });
      logger.error('Cache get operation failed:', apiError);
      return null; // Return null instead of throwing for cache misses
    }
  }

  /**
   * Delete cache entry
   */
  async delete(key) {
    try {
      let deleted = false;

      // Delete from Redis
      if (this.isConnected && this.redis) {
        try {
          await this.redis.del(key);
          deleted = true;
          this.metrics.redisOperations++;
        } catch (redisError) {
          logger.warn('Redis delete failed:', redisError.message);
          this.metrics.errors++;
        }
      }

      // Delete from fallback cache
      this.fallbackCache.del(key);
      this.metrics.fallbackOperations++;

      // Remove from warmup keys
      this.warmupKeys.delete(key);

      return deleted;

    } catch (error) {
      this.metrics.errors++;
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'cache',
        operation: 'delete',
        key
      });
      logger.error('Cache delete operation failed:', apiError);
      throw apiError;
    }
  }

  /**
   * Clear cache by pattern
   */
  async clearPattern(pattern) {
    try {
      let deletedCount = 0;

      // Clear from Redis using SCAN
      if (this.isConnected && this.redis) {
        try {
          const stream = this.redis.scanStream({
            match: pattern,
            count: 100
          });

          stream.on('data', async (keys) => {
            if (keys.length > 0) {
              await this.redis.del(...keys);
              deletedCount += keys.length;
            }
          });

          await new Promise((resolve) => {
            stream.on('end', resolve);
          });

          this.metrics.redisOperations++;
        } catch (redisError) {
          logger.warn('Redis pattern clear failed:', redisError.message);
          this.metrics.errors++;
        }
      }

      // Clear from fallback cache
      const regex = new RegExp(pattern.replace('*', '.*'));
      const fallbackKeys = this.fallbackCache.keys();
      
      fallbackKeys.forEach(key => {
        if (regex.test(key)) {
          this.fallbackCache.del(key);
          deletedCount++;
        }
      });

      this.metrics.fallbackOperations++;

      logger.info(`Cleared ${deletedCount} cache entries matching pattern: ${pattern}`);
      return deletedCount;

    } catch (error) {
      this.metrics.errors++;
      const apiError = enhancedErrorHandlingService.createAPIError(error, {
        service: 'cache',
        operation: 'clearPattern',
        pattern
      });
      logger.error('Cache pattern clear failed:', apiError);
      throw apiError;
    }
  }

  /**
   * Update compression statistics
   */
  updateCompressionStats(key, originalSize, compressedSize) {
    const strategy = this.getStrategyForKey(key);
    const stats = this.compressionStats.get(strategy.name) || {
      totalOriginal: 0,
      totalCompressed: 0,
      count: 0
    };

    stats.totalOriginal += originalSize;
    stats.totalCompressed += compressedSize;
    stats.count++;

    this.compressionStats.set(strategy.name, stats);
  }

  /**
   * Update average response time
   */
  updateResponseTime(duration) {
    const alpha = 0.1; // Smoothing factor for exponential moving average
    this.metrics.averageResponseTime = 
      (alpha * duration) + ((1 - alpha) * this.metrics.averageResponseTime);
  }

  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.logPerformanceMetrics();
    }, 60000); // Every minute
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics() {
    const hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests * 100).toFixed(2)
      : 0;

    const compressionRatio = this.getOverallCompressionRatio();

    logger.info('Cache performance metrics:', {
      hitRate: `${hitRate}%`,
      totalRequests: this.metrics.totalRequests,
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      errors: this.metrics.errors,
      averageResponseTime: `${this.metrics.averageResponseTime.toFixed(2)}ms`,
      redisOperations: this.metrics.redisOperations,
      fallbackOperations: this.metrics.fallbackOperations,
      compressionSavings: `${(this.metrics.compressionSavings / 1024).toFixed(2)}KB`,
      compressionRatio: `${compressionRatio.toFixed(2)}%`,
      isRedisConnected: this.isConnected
    });
  }

  /**
   * Get overall compression ratio
   */
  getOverallCompressionRatio() {
    let totalOriginal = 0;
    let totalCompressed = 0;

    for (const stats of this.compressionStats.values()) {
      totalOriginal += stats.totalOriginal;
      totalCompressed += stats.totalCompressed;
    }

    return totalOriginal > 0 
      ? ((totalOriginal - totalCompressed) / totalOriginal * 100)
      : 0;
  }

  /**
   * Get comprehensive cache status
   */
  getStatus() {
    const hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests * 100)
      : 0;

    return {
      isConnected: this.isConnected,
      redisEnabled: this.config.redis.enabled,
      metrics: {
        ...this.metrics,
        hitRate: parseFloat(hitRate.toFixed(2)),
        compressionRatio: parseFloat(this.getOverallCompressionRatio().toFixed(2))
      },
      strategies: Object.keys(CACHE_STRATEGIES),
      compressionStats: Object.fromEntries(this.compressionStats),
      warmupKeysCount: this.warmupKeys.size,
      fallbackCacheStats: this.fallbackCache.getStats()
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down cache service...');
    
    try {
      if (this.redis) {
        await this.redis.quit();
      }
      
      this.fallbackCache.flushAll();
      this.isConnected = false;
      
      logger.info('Cache service shut down gracefully');
    } catch (error) {
      logger.error('Error during cache shutdown:', error);
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const intelligentCacheService = new IntelligentCacheService();

export default intelligentCacheService;
export { IntelligentCacheService, CACHE_STRATEGIES };
