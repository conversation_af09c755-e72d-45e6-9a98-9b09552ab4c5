/**
 * Enhanced Service Worker for Trading Signals App
 *
 * Provides comprehensive offline functionality with:
 * - Multi-tier caching strategies
 * - Background sync for critical data
 * - Intelligent cache management
 * - Offline-first approach for static assets
 * - Network-first for real-time data
 * - Fallback strategies for different content types
 *
 * @version 2.0.0
 */

const CACHE_VERSION = '2.0.0';
const CACHE_NAME = `trading-signals-v${CACHE_VERSION}`;
const STATIC_CACHE = `trading-signals-static-v${CACHE_VERSION}`;
const API_CACHE = `trading-signals-api-v${CACHE_VERSION}`;
const MARKET_DATA_CACHE = `trading-signals-market-v${CACHE_VERSION}`;
const USER_DATA_CACHE = `trading-signals-user-v${CACHE_VERSION}`;

// Cache strategies configuration
const CACHE_STRATEGIES = {
  STATIC: 'cache-first',
  API: 'network-first',
  MARKET_DATA: 'network-first-short',
  USER_DATA: 'network-first',
  FALLBACK: 'cache-only'
};

// Cache TTL settings (in milliseconds)
const CACHE_TTL = {
  STATIC: 24 * 60 * 60 * 1000, // 24 hours
  API: 5 * 60 * 1000, // 5 minutes
  MARKET_DATA: 30 * 1000, // 30 seconds
  USER_DATA: 60 * 60 * 1000 // 1 hour
};

// Static files to cache
const STATIC_FILES = [
  '/',
  '/index.html',
  '/login.html',
  '/offline.html',
  '/manifest.json',
  '/css/styles.css',
  '/js/app.js',
  '/js/chart.js',
  '/js/trading-signals.js',
  '/js/enhanced-api-client.js',
  '/js/error-handler.js',
  '/icon-192.png',
  '/icon-512.png'
];

// API endpoints to cache
const CACHEABLE_API_PATTERNS = [
  /^\/api\/market-data\//,
  /^\/api\/technical-indicators\//,
  /^\/api\/signals\//,
  /^\/api\/user\/preferences/,
  /^\/api\/economic-calendar\//
];

// Background sync tags
const SYNC_TAGS = {
  SIGNALS: 'background-sync-signals',
  USER_PREFERENCES: 'background-sync-preferences',
  MARKET_DATA: 'background-sync-market-data'
};

// ============================================================================
// SERVICE WORKER INSTALLATION
// ============================================================================

self.addEventListener('install', (event) => {
  console.log('[SW] Installing enhanced service worker v' + CACHE_VERSION);

  event.waitUntil(
    Promise.all([
      // Cache static files
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('[SW] Caching static files');
        return cache.addAll(STATIC_FILES);
      }),

      // Initialize other caches
      caches.open(API_CACHE),
      caches.open(MARKET_DATA_CACHE),
      caches.open(USER_DATA_CACHE)
    ]).then(() => {
      console.log('[SW] Installation complete');
      // Force activation of new service worker
      return self.skipWaiting();
    }).catch((error) => {
      console.error('[SW] Installation failed:', error);
    })
  );
});

// ============================================================================
// SERVICE WORKER ACTIVATION
// ============================================================================

self.addEventListener('activate', (event) => {
  console.log('[SW] Activating enhanced service worker v' + CACHE_VERSION);

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      cleanupOldCaches(),

      // Claim all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[SW] Activation complete');

      // Notify clients about the new service worker
      notifyClientsAboutUpdate();
    })
  );
});

// ============================================================================
// FETCH EVENT HANDLING
// ============================================================================

self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Determine caching strategy based on request type
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isApiRequest(request)) {
    event.respondWith(handleApiRequest(request));
  } else if (isMarketDataRequest(request)) {
    event.respondWith(handleMarketDataRequest(request));
  } else if (isUserDataRequest(request)) {
    event.respondWith(handleUserDataRequest(request));
  } else {
    event.respondWith(handleGenericRequest(request));
  }
});

// ============================================================================
// BACKGROUND SYNC
// ============================================================================

self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  switch (event.tag) {
    case SYNC_TAGS.SIGNALS:
      event.waitUntil(syncSignals());
      break;
    case SYNC_TAGS.USER_PREFERENCES:
      event.waitUntil(syncUserPreferences());
      break;
    case SYNC_TAGS.MARKET_DATA:
      event.waitUntil(syncMarketData());
      break;
    default:
      console.log('[SW] Unknown sync tag:', event.tag);
  }
});

// ============================================================================
// MESSAGE HANDLING
// ============================================================================

self.addEventListener('message', (event) => {
  const { type, payload } = event.data;

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    case 'GET_CACHE_STATUS':
      event.ports[0].postMessage(getCacheStatus());
      break;
    case 'CLEAR_CACHE':
      clearSpecificCache(payload.cacheName).then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
    case 'FORCE_SYNC':
      forceSyncData(payload.syncTag);
      break;
    default:
      console.log('[SW] Unknown message type:', type);
  }
});

// ============================================================================
// CACHING STRATEGIES
// ============================================================================

/**
 * Handle static assets with cache-first strategy
 */
async function handleStaticAsset(request) {
  try {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, CACHE_TTL.STATIC)) {
      return cachedResponse;
    }

    // Fetch from network and update cache
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);
    }

    return networkResponse;
  } catch (error) {
    console.error('[SW] Static asset fetch failed:', error);

    // Return cached version if available
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }

    throw error;
  }
}

/**
 * Handle API requests with network-first strategy
 */
async function handleApiRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(API_CACHE);
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);

      return networkResponse;
    }

    throw new Error(`Network response not ok: ${networkResponse.status}`);
  } catch (error) {
    console.warn('[SW] API network request failed, trying cache:', error.message);

    // Fallback to cache
    const cache = await caches.open(API_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, CACHE_TTL.API)) {
      // Add offline indicator to response
      const response = cachedResponse.clone();
      response.headers.set('X-Served-By', 'service-worker-cache');
      response.headers.set('X-Cache-Status', 'offline');

      return response;
    }

    // No cache available, return error response
    return new Response(
      JSON.stringify({
        status: 'error',
        message: 'Service unavailable offline',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle market data with short-term network-first strategy
 */
async function handleMarketDataRequest(request) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(MARKET_DATA_CACHE);
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);

      return networkResponse;
    }

    throw new Error(`Network response not ok: ${networkResponse.status}`);
  } catch (error) {
    console.warn('[SW] Market data network request failed:', error.message);

    const cache = await caches.open(MARKET_DATA_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, CACHE_TTL.MARKET_DATA)) {
      const response = cachedResponse.clone();
      response.headers.set('X-Served-By', 'service-worker-cache');
      response.headers.set('X-Cache-Status', 'stale');

      return response;
    }

    // Return stale data with warning if available
    if (cachedResponse) {
      const response = cachedResponse.clone();
      response.headers.set('X-Served-By', 'service-worker-cache');
      response.headers.set('X-Cache-Status', 'stale-offline');

      return response;
    }

    return new Response(
      JSON.stringify({
        status: 'error',
        message: 'Market data unavailable offline',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle user data requests
 */
async function handleUserDataRequest(request) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(USER_DATA_CACHE);
      const responseToCache = networkResponse.clone();
      await cache.put(request, responseToCache);

      return networkResponse;
    }

    throw new Error(`Network response not ok: ${networkResponse.status}`);
  } catch (error) {
    const cache = await caches.open(USER_DATA_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, CACHE_TTL.USER_DATA)) {
      return cachedResponse;
    }

    return new Response(
      JSON.stringify({
        status: 'error',
        message: 'User data unavailable offline',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle generic requests
 */
async function handleGenericRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }
    throw error;
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if request is for a static asset
 */
function isStaticAsset(request) {
  const url = new URL(request.url);
  return STATIC_FILES.some(file => url.pathname === file) ||
         url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/);
}

/**
 * Check if request is for API data
 */
function isApiRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/') &&
         !isMarketDataRequest(request) &&
         !isUserDataRequest(request);
}

/**
 * Check if request is for market data
 */
function isMarketDataRequest(request) {
  const url = new URL(request.url);
  return url.pathname.includes('/api/market-data') ||
         url.pathname.includes('/api/technical-indicators') ||
         url.pathname.includes('/api/signals');
}

/**
 * Check if request is for user data
 */
function isUserDataRequest(request) {
  const url = new URL(request.url);
  return url.pathname.includes('/api/user/') ||
         url.pathname.includes('/api/auth/') ||
         url.pathname.includes('/api/preferences');
}

/**
 * Check if cached response is expired
 */
function isExpired(response, ttl) {
  const cachedTime = response.headers.get('sw-cached-time');
  if (!cachedTime) return true;

  const age = Date.now() - parseInt(cachedTime);
  return age > ttl;
}

/**
 * Clean up old caches
 */
async function cleanupOldCaches() {
  const cacheNames = await caches.keys();
  const currentCaches = [CACHE_NAME, STATIC_CACHE, API_CACHE, MARKET_DATA_CACHE, USER_DATA_CACHE];

  const deletePromises = cacheNames
    .filter(cacheName => !currentCaches.includes(cacheName))
    .map(cacheName => {
      console.log('[SW] Deleting old cache:', cacheName);
      return caches.delete(cacheName);
    });

  return Promise.all(deletePromises);
}

/**
 * Get cache status
 */
async function getCacheStatus() {
  const cacheNames = await caches.keys();
  const status = {};

  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    status[cacheName] = {
      size: keys.length,
      keys: keys.map(req => req.url)
    };
  }

  return status;
}

/**
 * Clear specific cache
 */
async function clearSpecificCache(cacheName) {
  return caches.delete(cacheName);
}

/**
 * Notify clients about service worker update
 */
async function notifyClientsAboutUpdate() {
  const clients = await self.clients.matchAll();
  clients.forEach(client => {
    client.postMessage({
      type: 'SW_UPDATED',
      version: CACHE_VERSION
    });
  });
}

/**
 * Notify clients about offline status
 */
async function notifyClientsAboutOfflineStatus() {
  const clients = await self.clients.matchAll();
  clients.forEach(client => {
    client.postMessage({
      type: 'OFFLINE_STATUS',
      message: 'You appear to be offline. Some features may not work properly.'
    });
  });
}

// ============================================================================
// BACKGROUND SYNC FUNCTIONS
// ============================================================================

/**
 * Sync signals data
 */
async function syncSignals() {
  try {
    console.log('[SW] Syncing signals data...');

    // Get pending signals from IndexedDB or localStorage
    const pendingSignals = await getPendingSignals();

    for (const signal of pendingSignals) {
      try {
        const response = await fetch('/api/signals', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(signal)
        });

        if (response.ok) {
          await removePendingSignal(signal.id);
          console.log('[SW] Signal synced successfully:', signal.id);
        }
      } catch (error) {
        console.error('[SW] Failed to sync signal:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Signal sync failed:', error);
  }
}

/**
 * Sync user preferences
 */
async function syncUserPreferences() {
  try {
    console.log('[SW] Syncing user preferences...');

    const pendingPreferences = await getPendingPreferences();

    for (const pref of pendingPreferences) {
      try {
        const response = await fetch('/api/user/preferences', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(pref)
        });

        if (response.ok) {
          await removePendingPreference(pref.id);
          console.log('[SW] Preferences synced successfully');
        }
      } catch (error) {
        console.error('[SW] Failed to sync preferences:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Preferences sync failed:', error);
  }
}

/**
 * Sync market data
 */
async function syncMarketData() {
  try {
    console.log('[SW] Syncing market data...');

    // Refresh critical market data
    const criticalEndpoints = [
      '/api/market-data/EURUSD',
      '/api/market-data/GBPUSD',
      '/api/market-data/USDJPY'
    ];

    for (const endpoint of criticalEndpoints) {
      try {
        const response = await fetch(endpoint);
        if (response.ok) {
          const cache = await caches.open(MARKET_DATA_CACHE);
          await cache.put(endpoint, response.clone());
        }
      } catch (error) {
        console.error('[SW] Failed to sync market data for:', endpoint);
      }
    }
  } catch (error) {
    console.error('[SW] Market data sync failed:', error);
  }
}

/**
 * Force sync data
 */
async function forceSyncData(syncTag) {
  try {
    await self.registration.sync.register(syncTag);
    console.log('[SW] Force sync registered for:', syncTag);
  } catch (error) {
    console.error('[SW] Failed to register force sync:', error);
  }
}

// ============================================================================
// INDEXEDDB/LOCALSTORAGE HELPERS
// ============================================================================

/**
 * Get pending signals (placeholder - implement with IndexedDB)
 */
async function getPendingSignals() {
  // TODO: Implement with IndexedDB
  return [];
}

/**
 * Remove pending signal (placeholder - implement with IndexedDB)
 */
async function removePendingSignal(signalId) {
  // TODO: Implement with IndexedDB
  console.log('[SW] Removing pending signal:', signalId);
}

/**
 * Get pending preferences (placeholder - implement with IndexedDB)
 */
async function getPendingPreferences() {
  // TODO: Implement with IndexedDB
  return [];
}

/**
 * Remove pending preference (placeholder - implement with IndexedDB)
 */
async function removePendingPreference(prefId) {
  // TODO: Implement with IndexedDB
  console.log('[SW] Removing pending preference:', prefId);
}

console.log('[SW] Enhanced service worker loaded v' + CACHE_VERSION);
