/**
 * UX Enhanced Component Example
 * 
 * Complete example demonstrating all Phase 3 user experience
 * improvements including contextual error messages, recovery
 * strategies, and accessibility features.
 * 
 * Features:
 * - Context-aware error messaging
 * - Multiple recovery strategies
 * - Full accessibility compliance
 * - Keyboard navigation support
 * - Screen reader optimization
 * - Progressive error disclosure
 * - User preference adaptation
 * 
 * @version 1.0.0
 */

import React, { useState, useEffect, useRef } from 'react';
import { useError } from '../../context/ErrorContext';
import { useSignalsData } from '../../hooks/useComponentState';
import EnhancedErrorDisplay from '../EnhancedErrorDisplay/EnhancedErrorDisplay';
import { 
  announceToScreenReader, 
  announceError, 
  announceSuccess,
  focusManager,
  KeyboardNavigationManager,
  setAriaAttributes
} from '../../utils/accessibilityHelpers';
import { setUserContext } from '../../utils/errorMessages';
import { RecoveryResult } from '../../services/errorRecoveryService';
import LoadingState from '../StateComponents/LoadingState';
import EmptyState from '../StateComponents/EmptyState';

// ============================================================================
// INTERFACES
// ============================================================================

interface UXEnhancedComponentProps {
  symbol?: string;
  userExperience?: 'new' | 'intermediate' | 'expert';
  accessibilityMode?: boolean;
  highContrastMode?: boolean;
  reducedMotion?: boolean;
}

interface UserPreferences {
  autoRetry: boolean;
  showTechnicalDetails: boolean;
  announceErrors: boolean;
  keyboardNavigation: boolean;
  reducedAnimations: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const UXEnhancedComponent: React.FC<UXEnhancedComponentProps> = ({
  symbol = 'AAPL',
  userExperience = 'intermediate',
  accessibilityMode = false,
  highContrastMode = false,
  reducedMotion = false
}) => {
  // State
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    autoRetry: true,
    showTechnicalDetails: userExperience === 'expert',
    announceErrors: accessibilityMode,
    keyboardNavigation: accessibilityMode,
    reducedAnimations: reducedMotion
  });

  const [recoveryInProgress, setRecoveryInProgress] = useState(false);
  const [lastRecoveryResult, setLastRecoveryResult] = useState<RecoveryResult | null>(null);
  const [errorDismissed, setErrorDismissed] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const keyboardNavRef = useRef<KeyboardNavigationManager | null>(null);
  const errorAnnouncedRef = useRef<string>('');

  // Data fetching with enhanced error handling
  const {
    data: signals,
    loading,
    error,
    isEmpty,
    refetch,
    retry,
    canRetry
  } = useSignalsData(symbol);

  // ========================================================================
  // INITIALIZATION AND CONTEXT SETUP
  // ========================================================================

  useEffect(() => {
    // Set user context for personalized error messages
    setUserContext({
      feature: 'trading-signals',
      userExperience,
      deviceType: window.innerWidth < 768 ? 'mobile' : 'desktop',
      timeOfDay: (() => {
        const hour = new Date().getHours();
        if (hour < 12) return 'morning';
        if (hour < 17) return 'afternoon';
        if (hour < 21) return 'evening';
        return 'night';
      })()
    });
  }, [userExperience]);

  // Set up keyboard navigation
  useEffect(() => {
    if (userPreferences.keyboardNavigation && containerRef.current) {
      keyboardNavRef.current = new KeyboardNavigationManager(containerRef.current, {
        trapFocus: false,
        escapeToClose: true,
        arrowNavigation: true,
        homeEndNavigation: true
      });

      // Listen for escape key to dismiss errors
      const handleEscape = () => {
        if (error && !errorDismissed) {
          handleErrorDismiss();
        }
      };

      containerRef.current.addEventListener('keyboardEscape', handleEscape);

      return () => {
        keyboardNavRef.current?.destroy();
        containerRef.current?.removeEventListener('keyboardEscape', handleEscape);
      };
    }
  }, [userPreferences.keyboardNavigation, error, errorDismissed]);

  // Announce errors to screen readers
  useEffect(() => {
    if (error && userPreferences.announceErrors) {
      const errorMessage = error.userMessage || error.message;
      
      // Only announce if it's a new error
      if (errorAnnouncedRef.current !== errorMessage) {
        announceError(errorMessage, canRetry ? 'Press R to retry' : undefined);
        errorAnnouncedRef.current = errorMessage;
      }
    }
  }, [error, userPreferences.announceErrors, canRetry]);

  // Announce successful recovery
  useEffect(() => {
    if (lastRecoveryResult?.success && userPreferences.announceErrors) {
      announceSuccess(lastRecoveryResult.message);
    }
  }, [lastRecoveryResult, userPreferences.announceErrors]);

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleRetry = async () => {
    setRecoveryInProgress(true);
    
    try {
      await retry();
      
      if (userPreferences.announceErrors) {
        announceSuccess('Data refreshed successfully');
      }
    } catch (retryError) {
      console.error('Retry failed:', retryError);
    } finally {
      setRecoveryInProgress(false);
    }
  };

  const handleRecovery = (result: RecoveryResult) => {
    setLastRecoveryResult(result);
    setRecoveryInProgress(false);
    
    if (result.success) {
      setErrorDismissed(true);
      
      // Focus back to main content
      if (containerRef.current) {
        focusManager.focusElement(containerRef.current);
      }
    }
  };

  const handleErrorDismiss = () => {
    setErrorDismissed(true);
    errorAnnouncedRef.current = '';
    
    if (userPreferences.announceErrors) {
      announceToScreenReader('Error dismissed');
    }
  };

  const handlePreferenceChange = (key: keyof UserPreferences, value: boolean) => {
    setUserPreferences(prev => ({ ...prev, [key]: value }));
    
    // Announce preference changes
    if (userPreferences.announceErrors) {
      announceToScreenReader(`${key} ${value ? 'enabled' : 'disabled'}`);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when component is focused
      if (!containerRef.current?.contains(document.activeElement)) return;

      switch (event.key.toLowerCase()) {
        case 'r':
          if (event.ctrlKey && canRetry) {
            event.preventDefault();
            handleRetry();
          }
          break;
        case 'escape':
          if (error && !errorDismissed) {
            event.preventDefault();
            handleErrorDismiss();
          }
          break;
        case 'f5':
          event.preventDefault();
          refetch();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [canRetry, error, errorDismissed, refetch]);

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderUserPreferences = () => (
    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold mb-3">Accessibility Preferences</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {Object.entries(userPreferences).map(([key, value]) => (
          <label key={key} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handlePreferenceChange(key as keyof UserPreferences, e.target.checked)}
              className="rounded border-gray-300 focus:ring-2 focus:ring-blue-500"
              aria-describedby={`${key}-description`}
            />
            <span className="text-sm font-medium">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </span>
          </label>
        ))}
      </div>
    </div>
  );

  const renderKeyboardShortcuts = () => (
    <div className="mb-4 p-3 bg-blue-50 rounded border border-blue-200">
      <h4 className="text-sm font-semibold text-blue-900 mb-2">Keyboard Shortcuts</h4>
      <ul className="text-xs text-blue-800 space-y-1">
        <li><kbd className="px-1 bg-blue-200 rounded">Ctrl+R</kbd> - Retry failed operation</li>
        <li><kbd className="px-1 bg-blue-200 rounded">F5</kbd> - Refresh data</li>
        <li><kbd className="px-1 bg-blue-200 rounded">Escape</kbd> - Dismiss error</li>
        <li><kbd className="px-1 bg-blue-200 rounded">Arrow Keys</kbd> - Navigate elements</li>
        <li><kbd className="px-1 bg-blue-200 rounded">Home/End</kbd> - Jump to first/last element</li>
      </ul>
    </div>
  );

  const renderContent = () => {
    // Show error if present and not dismissed
    if (error && !errorDismissed) {
      return (
        <EnhancedErrorDisplay
          error={error}
          context={{
            feature: 'trading-signals',
            component: 'signals-list',
            userJourney: 'viewing-signals'
          }}
          variant="full"
          showRecoveryOptions={true}
          showTechnicalDetails={userPreferences.showTechnicalDetails}
          onRecovered={handleRecovery}
          onDismiss={handleErrorDismiss}
        />
      );
    }

    // Show loading state
    if (loading) {
      return (
        <LoadingState
          variant="skeleton"
          message="Loading trading signals..."
        />
      );
    }

    // Show empty state
    if (isEmpty) {
      return (
        <EmptyState
          title="No Signals Available"
          description={`No trading signals found for ${symbol}. This could be due to market conditions or data availability.`}
          illustration="signals"
          actions={[
            {
              label: 'Refresh Data',
              onClick: () => refetch(),
              variant: 'primary'
            },
            {
              label: 'Try Different Symbol',
              onClick: () => console.log('Change symbol'),
              variant: 'outline'
            }
          ]}
        />
      );
    }

    // Show success state with data
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">
            Trading Signals for {symbol}
          </h3>
          <span className="text-sm text-gray-600">
            {signals?.length || 0} signals found
          </span>
        </div>

        {/* Recovery success message */}
        {lastRecoveryResult?.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded text-sm text-green-800">
            ✓ {lastRecoveryResult.message}
          </div>
        )}

        {/* Signals list */}
        <div className="grid gap-4">
          {signals?.map((signal, index) => (
            <div
              key={signal.id}
              className="p-4 border rounded-lg hover:shadow-md transition-shadow"
              tabIndex={0}
              role="article"
              aria-label={`Signal ${index + 1}: ${signal.type} ${signal.symbol}`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-semibold">{signal.symbol}</h4>
                  <p className="text-sm text-gray-600">{signal.type} Signal</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold">${signal.entryPrice}</span>
                  <p className="text-sm text-gray-600">{signal.confidence}% confidence</p>
                </div>
              </div>
              {signal.reasoning && (
                <p className="mt-2 text-sm text-gray-700">{signal.reasoning}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // ========================================================================
  // MAIN RENDER
  // ========================================================================

  return (
    <div
      ref={containerRef}
      className={`space-y-6 ${highContrastMode ? 'high-contrast' : ''}`}
      role="main"
      aria-label="Trading Signals Dashboard"
      tabIndex={-1}
    >
      {/* Skip link for keyboard users */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50"
      >
        Skip to main content
      </a>

      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Enhanced Trading Signals
        </h1>
        <p className="text-gray-600">
          Demonstrating advanced error handling and accessibility features
        </p>
      </div>

      {/* User preferences */}
      {renderUserPreferences()}

      {/* Keyboard shortcuts help */}
      {userPreferences.keyboardNavigation && renderKeyboardShortcuts()}

      {/* Main content */}
      <div id="main-content">
        {renderContent()}
      </div>

      {/* Status information for screen readers */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {loading && 'Loading data...'}
        {error && !errorDismissed && `Error: ${error.userMessage || error.message}`}
        {recoveryInProgress && 'Attempting to recover from error...'}
      </div>
    </div>
  );
};

export default UXEnhancedComponent;
